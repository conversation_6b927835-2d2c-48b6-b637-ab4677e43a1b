export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
  NO_CONTENT: 204,
};

export const CONTENT_TYPES = {
  PDF: 'application/pdf',
  EXE: 'application/x-msdownload',
  JSON: 'application/json',
  TEXT: 'text/plain',
  HTML: 'text/html',
  PNG: 'image/png',
  JPEG: 'image/jpeg',
  // Add more as needed
};

const SEVEN = 7;
export function getEndDate(
  startTimestamp: number,
  interval: string,
  intervalCount: number,
): {startDate: string; endDate: string} {
  // Convert to ms
  const startDateObj = new Date(startTimestamp * 1000);

  // Clone start date
  const endDateObj = new Date(startDateObj);

  // Add interval
  if (interval === 'day') {
    endDateObj.setDate(endDateObj.getDate() + intervalCount);
  } else if (interval === 'week') {
    endDateObj.setDate(endDateObj.getDate() + SEVEN * intervalCount);
  } else if (interval === 'month') {
    endDateObj.setMonth(endDateObj.getMonth() + intervalCount);
  } else if (interval === 'year') {
    endDateObj.setFullYear(endDateObj.getFullYear() + intervalCount);
  } else {
    // DO NOTHING
  }
  // Format YYYY-MM-DD
  const formatDate = (d: Date) => d.toISOString().split('T')[0];

  return {
    startDate: formatDate(startDateObj),
    endDate: formatDate(endDateObj),
  };
}
