import {expect} from '@loopback/testlab';
import * as jwt from 'jsonwebtoken';
import * as sinon from 'sinon';
import {CryptoHelperService} from '../../services/crypto-helper.service';

describe('CryptoHelperService', () => {
  let service: CryptoHelperService;
  const OLD_ENV = process.env;

  beforeEach(() => {
    service = new CryptoHelperService();
    process.env = {...OLD_ENV}; // clone env
  });

  afterEach(() => {
    sinon.restore();
    process.env = OLD_ENV; // restore env
  });

  describe('generateTempToken', () => {
    it('should generate a signed token with default expiry', () => {
      process.env.JWT_SECRET = 'my-secret';
      process.env.JWT_ISSUER = 'my-issuer';

      const payload = {userId: 'abc123'};

      const token = service.generateTempToken(payload);
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET as string,
      ) as jwt.JwtPayload & {userId: string; iss: string};

      expect(decoded.userId).to.equal('abc123');
      expect(decoded.iss).to.equal('my-issuer');
    });

    it('should generate a signed token with custom expiry', () => {
      process.env.JWT_SECRET = 'my-secret';
      process.env.JWT_ISSUER = 'issuer';
      const payload = {role: 'admin'};

      const token = service.generateTempToken(payload, 10); // 10 seconds expiry
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET as string,
      ) as jwt.JwtPayload & {role: string};

      expect(decoded.role).to.equal('admin');
    });

    it('should throw an error if JWT_SECRET is not defined', () => {
      delete process.env.JWT_SECRET;

      expect(() => service.generateTempToken({})).to.throw(
        'JWT_SECRET is not defined in the environment variables.',
      );
    });
  });

  describe('generateRandomString', () => {
    it('should generate a random string of correct length', () => {
      const length = 16;
      const result = service.generateRandomString(length);
      expect(result).to.be.a.String();
      expect(result.length).to.equal(length);
    });

    it('should generate different strings on multiple calls', () => {
      const str1 = service.generateRandomString(12);
      const str2 = service.generateRandomString(12);
      expect(str1).to.not.equal(str2);
    });
  });

  describe('encryptPassword and decryptPassword', () => {
    it('should encrypt and decrypt a password correctly', () => {
      // Create a proper 32-byte key for AES-256
      const key32Bytes = 'abcdefghijklmnopqrstuvwxyz123456'; // exactly 32 bytes
      process.env.SECRET_KEY = Buffer.from(key32Bytes).toString('base64');

      const originalPassword = 'mySecretPassword123';
      const encrypted = service.encryptPassword(originalPassword);
      const decrypted = service.decryptPassword(encrypted);

      expect(decrypted).to.equal(originalPassword);
      expect(encrypted).to.not.equal(originalPassword);
      expect(encrypted).to.match(/^[a-f0-9]{32}:[a-f0-9]+$/); // Should match iv:encryptedData format
    });

    it('should generate different encrypted values for the same password', () => {
      // Create a proper 32-byte key for AES-256
      const key32Bytes = 'abcdefghijklmnopqrstuvwxyz123456'; // exactly 32 bytes
      process.env.SECRET_KEY = Buffer.from(key32Bytes).toString('base64');

      const password = 'samePassword';
      const encrypted1 = service.encryptPassword(password);
      const encrypted2 = service.encryptPassword(password);

      expect(encrypted1).to.not.equal(encrypted2); // Different IVs should produce different results
      expect(service.decryptPassword(encrypted1)).to.equal(password);
      expect(service.decryptPassword(encrypted2)).to.equal(password);
    });

    it('should throw error when SECRET_KEY is not defined for encryption', () => {
      delete process.env.SECRET_KEY;

      expect(() => service.encryptPassword('test')).to.throw(
        'SECRET_KEY is not defined in the environment variables.',
      );
    });

    it('should throw error when SECRET_KEY is not defined for decryption', () => {
      delete process.env.SECRET_KEY;

      expect(() => service.decryptPassword('test:data')).to.throw(
        'SECRET_KEY is not defined in the environment variables.',
      );
    });

    it('should throw error for invalid encrypted password format', () => {
      // Create a proper 32-byte key for AES-256
      const key32Bytes = 'abcdefghijklmnopqrstuvwxyz123456'; // exactly 32 bytes
      process.env.SECRET_KEY = Buffer.from(key32Bytes).toString('base64');

      expect(() => service.decryptPassword('invalid-format')).to.throw(
        'Invalid encrypted password format. Expected format: "iv:encryptedData"',
      );
    });
  });
});
