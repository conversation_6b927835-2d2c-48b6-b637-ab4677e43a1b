import {diag, DiagConsoleLogger, DiagLogLevel} from '@opentelemetry/api';
import {NodeTracerProvider} from '@opentelemetry/sdk-trace-node';
import {SimpleSpanProcessor} from '@opentelemetry/sdk-trace-base';
import {OTLPTraceExporter} from '@opentelemetry/exporter-trace-otlp-http';
import {OTLPMetricExporter} from '@opentelemetry/exporter-metrics-otlp-http';
import {
  MeterProvider,
  PeriodicExportingMetricReader,
} from '@opentelemetry/sdk-metrics';
import {Resource} from '@opentelemetry/resources';
import {SemanticResourceAttributes} from '@opentelemetry/semantic-conventions';
import {registerInstrumentations} from '@opentelemetry/instrumentation';
import {HttpInstrumentation} from '@opentelemetry/instrumentation-http';
import {ExpressInstrumentation} from '@opentelemetry/instrumentation-express';
import {PgInstrumentation} from '@opentelemetry/instrumentation-pg';
import {AnyObject} from '@loopback/repository';

const SIGNOZ_ENABLED = process.env.SIGNOZ_ENABLED === 'true';
const SIXTY_THOUSAND = 60000;
// prettier-ignore
let recordRequestMetrics: ( // NOSONAR
  req: AnyObject,
  res: AnyObject,
  next: Function,
) => void = (req, res, next) => {
  next();
};

if (SIGNOZ_ENABLED) {
  diag.setLogger(new DiagConsoleLogger(), DiagLogLevel.INFO);

  const OTLP_ENDPOINT = process.env.OTLP_ENDPOINT ?? 'http://localhost:4318';
  const SERVICE_NAME = process.env.SERVICE_NAME ?? 'tenant-mgmt-facade-new';
  const APP_NAME = process.env.APP_NAME ?? 'distek-control-plane';
  const ENVIRONMENT = process.env.ENVIRONMENT ?? 'dev';

  const resource = new Resource({
    [SemanticResourceAttributes.SERVICE_NAME]: SERVICE_NAME,
    [SemanticResourceAttributes.SERVICE_NAMESPACE]: APP_NAME,
    [SemanticResourceAttributes.DEPLOYMENT_ENVIRONMENT]: ENVIRONMENT,
    [SemanticResourceAttributes.SERVICE_VERSION]: '1.0.0',
  });

  // ---- Tracing Setup ----
  const traceExporter = new OTLPTraceExporter({
    url: `${OTLP_ENDPOINT}/v1/traces`,
  });
  const tracerProvider = new NodeTracerProvider({resource});
  tracerProvider.addSpanProcessor(new SimpleSpanProcessor(traceExporter));
  tracerProvider.register();

  // ---- Metrics Setup ----
  const metricExporter = new OTLPMetricExporter({
    url: `${OTLP_ENDPOINT}/v1/metrics`,
  });
  const meterProvider = new MeterProvider({resource});

  meterProvider.addMetricReader(
    new PeriodicExportingMetricReader({
      exporter: metricExporter,
      exportIntervalMillis: SIXTY_THOUSAND,
    }),
  );

  // Register meter provider globally to enable automatic instrumentation metrics
  //   meterProvider.register();

  const meter = meterProvider.getMeter(APP_NAME);

  // ---- Runtime Metrics ----
  const memoryUsageGauge = meter.createObservableGauge(
    'nodejs_memory_usage_bytes',
    {
      description: 'Node.js memory usage in bytes',
    },
  );
  memoryUsageGauge.addCallback(observableResult => {
    const mem = process.memoryUsage();
    observableResult.observe(mem.rss, {type: 'rss'});
    observableResult.observe(mem.heapUsed, {type: 'heapUsed'});
    observableResult.observe(mem.heapTotal, {type: 'heapTotal'});
    observableResult.observe(mem.external, {type: 'external'});
  });

  const cpuUsageCounter = meter.createCounter('nodejs_cpu_seconds_total', {
    description: 'Total user + system CPU time spent in seconds',
  });

  setInterval(() => {
    const cpuUsage = process.cpuUsage();
    cpuUsageCounter.add(cpuUsage.user / 1e6, {mode: 'user'});
    cpuUsageCounter.add(cpuUsage.system / 1e6, {mode: 'system'});
  }, SIXTY_THOUSAND);

  const eventLoopLagGauge = meter.createObservableGauge(
    'nodejs_eventloop_lag_ms',
    {
      description: 'Event loop lag in ms',
    },
  );
  eventLoopLagGauge.addCallback(observableResult => {
    const start = Date.now();
    setImmediate(() => {
      const lag = Date.now() - start;
      observableResult.observe(lag);
    });
  });

  // ---- Instrumentations ----
  registerInstrumentations({
    instrumentations: [
      new HttpInstrumentation(),
      new ExpressInstrumentation(),
      new PgInstrumentation(),
    ],
    tracerProvider,
    meterProvider,
  });

  console.info(
    `Tracing + Metrics initialized: ${SERVICE_NAME} in app: ${APP_NAME}`,
  );

  // ---- Middleware for custom HTTP metrics ----
  const requestCounter = meter.createCounter('http_server_requests_total', {
    description: 'Total number of HTTP requests',
  });
  const latencyHistogram = meter.createHistogram(
    'http_server_request_duration_ms',
    {
      description: 'Duration of HTTP requests in ms',
    },
  );
  const activeRequests = meter.createUpDownCounter(
    'http_server_active_requests',
    {
      description: 'Current number of in-flight HTTP requests',
    },
  );

  recordRequestMetrics = function (
    req: AnyObject,
    res: AnyObject,
    next: Function,
  ) {
    activeRequests.add(1);

    const start = Date.now();
    res.on('finish', () => {
      activeRequests.add(-1);
      const duration = Date.now() - start;

      const labels = {
        /* eslint-disable @typescript-eslint/naming-convention */
        app_name: APP_NAME,
        /* eslint-disable @typescript-eslint/naming-convention */
        service_name: SERVICE_NAME,
        method: req.method,
        route: req.route?.path || req.path || 'unknown',
        /* eslint-disable @typescript-eslint/naming-convention */
        status_code: res.statusCode.toString(),
      };

      requestCounter.add(1, labels);
      latencyHistogram.record(duration, labels);
    });

    next();
  };
}

export {recordRequestMetrics};
