import {Filter, FilterBuilder, Where} from '@loopback/repository';

export class MergeFilterBuilder<T extends object> extends FilterBuilder<T> {
  constructor(filter?: Filter<T>) {
    super(filter);
  }

  /**
   * Merge new where conditions into the existing ones using `and`
   * If parent already has `and`, flatten it instead of nesting.
   */
  mergeWhere(where: Where<T>): this {
    const existing = this.build().where;

    if (!existing) {
      super.where(where);
      return this;
    }

    // If existing is already an `and`
    if ('and' in existing) {
      super.where({
        and: [...existing.and, where],
      });
    }
    // If existing is a plain object
    else {
      super.where({
        and: [existing, where],
      });
    }

    return this;
  }
}
