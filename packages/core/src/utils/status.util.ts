export function getEnumMap<T extends Record<string, string | number>>(
  enumObj: T,
): {[key: number]: string} {
  return Object.keys(enumObj)
    .filter(key => !isNaN(Number(key))) // keep only numeric keys
    .reduce(
      (acc, key) => {
        const numKey = Number(key);
        // enumObj[numKey] will be string | number, so cast to string safely
        acc[numKey] = String(enumObj[numKey]);
        return acc;
      },
      {} as {[key: number]: string},
    );
}

export function numericEnumValues(enumType: Object) {
  return Object.keys(enumType)
    .map(key => Number(key))
    .filter(value => !isNaN(value));
}
/**
 * Splits an array into chunks of the specified size.
 *
 * @template T - The type of elements in the array.
 * @param {T[]} arr - The input array to be split.
 * @param {number} size - The maximum size of each chunk. Must be greater than 0.
 * @returns {T[][]} A new array containing sub-arrays (chunks), each with up to `size` elements.
 *
 * @example
 * chunk([1, 2, 3, 4, 5], 2);
 * // Returns: [[1, 2], [3, 4], [5]]
 */
export function chunk<T>(arr: T[], size: number): T[][] {
  const chunks: T[][] = [];
  for (let i = 0; i < arr.length; i += size) {
    chunks.push(arr.slice(i, i + size));
  }
  return chunks;
}
