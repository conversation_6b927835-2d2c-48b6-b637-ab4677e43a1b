{"name": "@local/core", "version": "0.0.1", "description": "core", "keywords": ["loopback-extension", "loopback"], "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=18"}, "scripts": {"build": "lb-tsc", "build:watch": "lb-tsc --watch", "lint": "npm run eslint && npm run prettier:check", "lint:fix": "npm run eslint:fix && npm run prettier:fix", "prettier:cli": "prettier \"**/*.ts\" \"**/*.js\"", "prettier:check": "npm run prettier:cli -- -l", "prettier:fix": "npm run prettier:cli -- --write", "eslint": "eslint --report-unused-disable-directives .", "eslint:fix": "npm run eslint -- --fix", "pretest": "npm run rebuild", "test": "lb-mocha --allow-console-logs \"dist/__tests__\"", "test:dev": "lb-mocha --allow-console-logs dist/__tests__/**/*.js", "clean": "lb-clean dist *.tsbuildinfo .eslintcache", "rebuild": "npm run clean && npm run build", "postinstall": "npm run build", "prune": "npm prune --production", "coverage": "nyc npm run test"}, "repository": {"type": "git", "url": ""}, "author": "", "license": "MIT", "files": ["README.md", "dist", "src", "!*/__tests__"], "peerDependencies": {"@loopback/core": "^5.1.5"}, "dependencies": {"@loopback/context": "^6.1.5", "@loopback/rest": "^13.1.5", "@loopback/rest-explorer": "^6.1.5", "@sourceloop/file-utils": "^0.2.1", "loopback4-notifications": "^9.0.1", "loopback4-s3": "^9.0.3", "stripe": "^18.4.0", "tslib": "^2.0.0", "@opentelemetry/exporter-jaeger": "^1.15.0", "@opentelemetry/plugin-dns": "^0.15.0", "@opentelemetry/plugin-http": "^0.18.2", "@opentelemetry/plugin-https": "^0.18.2", "@opentelemetry/plugin-pg": "^0.15.0", "@opentelemetry/plugin-pg-pool": "^0.15.0", "@opentelemetry/semantic-conventions": "^1.25.1", "@opentelemetry/sdk-trace-base": "^1.15.0", "@opentelemetry/sdk-trace-node": "^1.15.0", "@opentelemetry/sdk-metrics": "^1.15.0", "@opentelemetry/sdk-logs": "^0.52.0", "@opentelemetry/sdk-node": "^0.52.0", "@opentelemetry/instrumentation": "^0.52.0", "@opentelemetry/instrumentation-http": "^0.52.0", "@opentelemetry/instrumentation-express": "^0.52.0", "@opentelemetry/exporter-trace-otlp-http": "^0.52.0", "@opentelemetry/exporter-metrics-otlp-http": "^0.52.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.52.0", "@opentelemetry/resources": "^1.9.0"}, "devDependencies": {"nise": "^5.0.0", "@loopback/build": "^10.1.5", "@loopback/core": "^5.1.5", "@loopback/eslint-config": "^14.0.5", "@loopback/testlab": "^6.1.5", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^16.18.70", "eslint": "^8.51.0", "source-map-support": "^0.5.21", "typescript": "~5.2.2"}}