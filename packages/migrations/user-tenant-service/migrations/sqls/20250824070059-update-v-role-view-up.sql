DROP VIEW IF EXISTS main.v_roles;
    CREATE VIEW main.v_roles AS
    SELECT r.id AS role_id,
           r.name AS role_name,
           r.deleted,
           r.created_on,
           r.modified_on,
           r.role_type,
           r.deleted_by,
           r.deleted_on,
           r.tenant_id,
           COUNT(ut.user_id) AS user_count
    FROM main.roles r
    LEFT JOIN main.user_tenants ut ON r.id = ut.role_id
    GROUP BY r.id;