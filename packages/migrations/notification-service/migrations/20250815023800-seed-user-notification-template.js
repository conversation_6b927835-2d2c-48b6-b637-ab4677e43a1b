'use strict';

const fs = require('fs');
const path = require('path');

function runSql(fileName, db) {
  const sql = fs.readFileSync(path.join(__dirname, 'sqls', fileName), { encoding: 'utf-8' });
  return db.runSql(sql);
}

exports.up = function(db, callback) {
  runSql('20250815023800-seed-user-notification-template-up.sql', db);
  callback();
};

exports.down = function(db, callback) {
  runSql('20250815023800-seed-user-notification-template-down.sql', db);
  callback();
};

exports._meta = {
  version: 1,
};