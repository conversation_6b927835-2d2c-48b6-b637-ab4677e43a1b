/* Replace with your SQL commands */
UPDATE main.notification_templates
SET body = '<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Payment Page</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: Arial, sans-serif;
      background-color: #f5f7fb;
    }
    .container {
      width: 100%;
      max-width: 600px;
      margin: 40px auto;
      background: #ffffff;
      border-radius: 10px;
      box-shadow: 0 6px 24px rgba(18, 38, 63, 0.08);
      padding: 24px;
      text-align: center;
    }
    h1 {
      font-size: 22px;
      margin-bottom: 12px;
      color: #1f2937;
    }
    p {
      font-size: 15px;
      color: #6b7280;
      margin-bottom: 24px;
    }
    .btn {
      display: inline-block;
      background: linear-gradient(135deg, #2563eb, #3b82f6);
      color: #ffffff !important;
      font-weight: 700;
      padding: 14px 22px;
      border-radius: 8px;
      text-decoration: none;
      font-size: 16px;
    }
    .btn:hover {
      opacity: 0.95;
    }
    .footer {
      font-size: 12px;
      color: #6b7280;
      margin-top: 24px;
    }
    .footer a {
      color: #2563eb;
      text-decoration: none;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Make a Payment</h1>
    <p>You can complete your payment securely by clicking the button below.</p>
    <a href="{{paymentLink}}" class="btn" target="_blank">Proceed to Payment</a>
  </div>
</body>
</html>'
WHERE event_name = 'invoice_payment_link';
