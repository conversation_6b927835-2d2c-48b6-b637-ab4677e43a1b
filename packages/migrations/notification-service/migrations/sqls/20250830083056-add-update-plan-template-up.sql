/* Replace with your SQL commands */
INSERT INTO main.notification_templates(event_name, body, subject, notification_type)
VALUES (
  'plan_update',
  '<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Plan Update</title>
  <style>
    body {
      font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f4f6f8;
      margin: 0;
      padding: 0;
      color: #333333;
    }
    .container {
      max-width: 650px;
      margin: 40px auto;
      background: #ffffff;
      padding: 35px;
      border-radius: 10px;
      box-shadow: 0 4px 10px rgba(0,0,0,0.08);
    }
    h2 {
      color: #2c3e50;
      font-size: 22px;
      text-align: center;
      margin-bottom: 25px;
    }
    .details {
      font-size: 15px;
      line-height: 1.6;
    }
    .details p {
      margin: 10px 0;
    }
    .highlight {
      font-weight: bold;
      color: #007bff;
    }
    .footer {
      margin-top: 30px;
      font-size: 13px;
      color: #777777;
      text-align: center;
      border-top: 1px solid #eaeaea;
      padding-top: 15px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h2>Your Subscription Plan Has Been Updated</h2>
    <div class="details">
      <p><span class="highlight">Plan Name:</span> {{planName}}</p>
      <p><span class="highlight">Price:</span> ${{planPrice}}</p>
      <p><span class="highlight">Unlimited Users Allowed:</span> {{#if allowUnlimitedUsers}}Yes{{else}}No{{/if}}</p>
      <p><span class="highlight">Cost Per User:</span> ${{costPerUser}}</p>
      <p><span class="highlight">Total Cost:</span> ${{totalCost}}</p>
      <p>From next billing cycle, updated changes will be applied</p>
    </div>
    <div class="footer">
      If you have any questions regarding this update, please contact our support team.<br>
      Thank you for choosing us!
    </div>
  </div>
</body>
</html>',
  'Your Subscription Plan Has Been Updated',
  1
);
