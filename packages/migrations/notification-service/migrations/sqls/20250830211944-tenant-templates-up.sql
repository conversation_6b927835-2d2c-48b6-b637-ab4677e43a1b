INSERT INTO main.notification_templates(event_name, body, subject, notification_type)
VALUES (
  'tenant_onboarding',
  '<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Pay Invoice</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #f7f7f7;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 40px auto;
      background: #ffffff;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      text-align: center;
    }
    .pay-button {
      display: inline-block;
      margin-top: 20px;
      padding: 12px 24px;
      background-color: #007bff;
      color: #ffffff !important;
      text-decoration: none;
      font-size: 16px;
      font-weight: bold;
      border-radius: 6px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h2>{{emailDetails}}</h2>
   <h3>Tenant Details</h3>
   
   <p><strong>Tenant Name:</strong> {{tenantName}}</p>
   <p><strong>Tenant URL:</strong><a href="{{tenantId}}">{{tenantId}}</a></p>
  
   <h3>Subscription Details</h3>
   <p><strong>Subscription ID:</strong> {{subscriptionId}}</p>
   <p><strong>Subscription Plan:</strong> {{subscriptionPlan}}</p>
    <p><strong>Subscription Status:</strong> {{subscriptionStatus}}</p>

    <h3>Admin Details</h3>
    <p><strong>Admin Name:</strong> {{adminName}}</p>
    <p><strong>Admin Email:</strong> {{adminEmail}}</p>
    
    <p><strong>Set Password:</strong> <a href="{{passwordLink}}">{{passwordLink}}</a></p>
  </div>
</body>
</html>',
  'Tenant Provisioning',
  1
);
