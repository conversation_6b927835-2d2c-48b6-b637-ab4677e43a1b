#!/usr/bin/env node

/**
 * Utility script to generate a proper SECRET_KEY for AES-256 encryption
 * 
 * Usage: node generate-secret-key.js
 * 
 * This will generate a base64-encoded 32-byte key suitable for AES-256 encryption.
 */

const crypto = require('crypto');

// Generate a random 32-byte key for AES-256
const key = crypto.randomBytes(32);

// Convert to base64 for storage in environment variables
const base64Key = key.toString('base64');

console.log('Generated SECRET_KEY for AES-256 encryption:');
console.log('');
console.log(`SECRET_KEY=${base64Key}`);
console.log('');
console.log('Key details:');
console.log(`- Raw key length: ${key.length} bytes`);
console.log(`- Base64 encoded length: ${base64Key.length} characters`);
console.log('');
console.log('Add this to your .env file or environment variables.');
console.log('');
console.log('⚠️  IMPORTANT: Keep this key secure and never commit it to version control!');
