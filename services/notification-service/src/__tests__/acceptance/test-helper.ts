import {NotificationServiceApplication} from '../..';
import {
  createRestAppClient,
  givenHttpServerConfig,
  Client,
} from '@loopback/testlab';
import {NotifDbSourceName} from '@sourceloop/notification-service';

import {sign} from 'jsonwebtoken';
export async function setupApplication(): Promise<AppWithClient> {
  const restConfig = givenHttpServerConfig({
    // Customize the server configuration here.
    // Empty values (undefined, '') will be ignored by the helper.
    //
    // host: process.env.HOST,
    // port: +process.env.PORT,
  });
  setUpEnv();

  const app = new NotificationServiceApplication({
    rest: restConfig,
  });

  app.bind(`datasources.config.${NotifDbSourceName}`).to({
    name: NotifDbSourceName,
    connector: 'sqlite3',
    file: ':memory:',
  });

  await app.boot();
  await app.start();

  const client = createRestAppClient(app);

  return {app, client};
}

function setUpEnv() {
  process.env.NODE_ENV = 'test';
  process.env.ENABLE_TRACING = '0';
  process.env.ENABLE_OBF = '0';
  process.env.HOST = 'localhost';
  process.env.JWT_ISSUER = 'test';
  process.env.JWT_SECRET = '6356345364';
}

export interface AppWithClient {
  app: NotificationServiceApplication;
  client: Client;
}

export function getToken(permissions: string[] = [], withoutBearer = false) {
  return `${withoutBearer ? '' : 'Bearer '}${sign(
    {
      id: 'test',
      userTenantId: 'test',
      iss: process.env.JWT_ISSUER,
      permissions,
    },
    process.env.JWT_SECRET ?? '',
  )}`;
}
