import {inject, service} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  get,
  getModelSchemaRef,
  HttpErrors,
  param,
  patch,
  post,
  requestBody,
  RestBindings,
} from '@loopback/rest';
import {
  CONTENT_TYPE,
  OPERATION_SECURITY_SPEC,
  STATUS_CODE,
} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {CreateLeadDTO, Lead} from '../models';
import {LeadRepository} from '../repositories';
import {OnboardingService} from '../services';
import {getEnumMap, LeadStatus, PermissionKey, StatusDto} from '@local/core';
import {ILeadOnboardingService} from '../types';

const basePath = '/leads';
const leadDescription = 'Lead model instance';

export class LeadController {
  constructor(
    @repository(LeadRepository)
    public leadRepository: LeadRepository,
    @service(OnboardingService)
    public onboarding: ILeadOnboardingService<Lead>,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
  ) {}

  @authorize({
    permissions: [PermissionKey.CreateLead],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @post(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: leadDescription,
        content: {
          [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Lead)},
        },
      },
    },
  })
  /**
   * Creates a new lead using the provided lead data.
   *
   * @param lead - The lead information to create, excluding `isValidated`, `addressId`, and `id` fields.
   * @returns A promise that resolves to the newly created `Lead` entity.
   */
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(CreateLeadDTO, {
            title: 'CreateLeadDTO',
            exclude: ['isValidated', 'addressId', 'id'],
          }),
        },
      },
    })
    lead: Omit<CreateLeadDTO, 'isValidated' | 'addressId' | 'id'>,
  ): Promise<Lead> {
    return this.onboarding.addLead(lead);
  }

  @authorize({
    permissions: [PermissionKey.ViewLead],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/count`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Lead model count',
        content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
      },
    },
  })
  /**
   * Returns the count of Lead entities that match the specified criteria.
   *
   * @param where - Optional filter criteria to count only matching Lead entities.
   * @returns A promise that resolves to the count of matching Lead entities.
   */
  async count(@param.where(Lead) where?: Where<Lead>): Promise<Count> {
    return this.leadRepository.count(where);
  }

  @authorize({
    permissions: [PermissionKey.ViewLead],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Lead model instances',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(Lead, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  /**
   * Retrieves a list of `Lead` entities from the repository, optionally filtered by the provided criteria.
   *
   * @param filter - Optional filter object to specify query conditions for retrieving leads.
   * @returns A promise that resolves to an array of `Lead` objects matching the filter.
   */
  async find(@param.filter(Lead) filter?: Filter<Lead>): Promise<Lead[]> {
    return this.leadRepository.find(filter);
  }

  @authorize({
    permissions: [PermissionKey.ViewLead],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/{id}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: leadDescription,
        content: {
          [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Lead)},
        },
      },
    },
  })
  /**
   * Retrieves a Lead entity by its unique identifier.
   *
   * @param id - The unique identifier of the Lead to retrieve.
   * @param filter - Optional filter object to specify fields and relations to include, excluding the 'where' clause.
   * @returns A promise that resolves to the Lead entity matching the provided id.
   */
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Lead, {exclude: 'where'})
    filter?: Filter<Lead>,
  ): Promise<Lead> {
    return this.leadRepository.findById(id, filter);
  }

  @authorize({
    permissions: [PermissionKey.UpdateLead],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @patch(`${basePath}/{id}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Lead PATCH success',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: getModelSchemaRef(Lead),
          },
        },
      },
    },
  })
  /**
   * Updates a lead by its ID if it has not been converted or marked as invalid.
   *
   * @param id - The unique identifier of the lead to update.
   * @param lead - The partial lead object containing updated fields.
   * @throws {HttpErrors.BadRequest} If the lead has already been converted or is invalid.
   * @returns A promise that resolves when the update is complete.
   */
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Lead, {partial: true}),
        },
      },
    })
    lead: Lead,
  ): Promise<void> {
    const leadDetails = await this.leadRepository.findById(id);
    if (
      leadDetails.status === LeadStatus.CONVERTED ||
      leadDetails.status === LeadStatus.INVALID
    ) {
      throw new HttpErrors.BadRequest(
        'Cannot update a converted or invalid lead',
      );
    }

    await this.leadRepository.updateById(id, lead);
  }

  @authorize({
    permissions: [PermissionKey.ViewAllStatuses],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/all-status`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Object of all possible lead status',
        permissions: [PermissionKey.ViewAllStatuses],
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'object',
              items: getModelSchemaRef(StatusDto, {
                title: 'LeadStatusDto',
              }),
            },
          },
        },
      },
    },
  })
  /**
   * Retrieves a mapping of all possible lead statuses.
   *
   * @returns {Promise<StatusDto>} A promise that resolves to a StatusDto containing the available lead statuses.
   */
  async findAllStatus(): Promise<StatusDto> {
    const statusMap = getEnumMap(LeadStatus);
    return new StatusDto({statuses: statusMap});
  }
}
