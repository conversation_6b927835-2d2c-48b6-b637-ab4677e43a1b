import {injectable, BindingScope} from '@loopback/core';
import {Contact, Tenant, UpdateTenantDto} from '../models';
import {FileRepository, TenantRepository} from '../repositories';
import {repository} from '@loopback/repository';
import {
  Address,
  AddressRepository,
  ContactRepository,
} from '@sourceloop/ctrl-plane-tenant-management-service';
@injectable({scope: BindingScope.TRANSIENT})
export class TenantHelperService {
  constructor(
    @repository(TenantRepository)
    private tenantRepository: TenantRepository<Tenant>,
    @repository(ContactRepository)
    private contactRepository: ContactRepository<Contact>,
    @repository(AddressRepository)
    private addressRepository: AddressRepository<Address>,
    @repository(FileRepository)
    private fileRepository: FileRepository,
  ) {}

  async updateTenant(id: string, dto: Partial<UpdateTenantDto>): Promise<void> {
    const transaction = await this.tenantRepository.beginTransaction();
    const {selectedFiles, contact, city, state, ...rest} = dto;
    try {
      if (contact && Object.keys(contact).length > 0)
        await this.contactRepository.updateAll(
          contact,
          {tenantId: id},
          {transaction},
        );

      await this.tenantRepository.updateById(id, rest, {transaction});

      const updatedTenant = await this.tenantRepository.findById(
        id,
        {include: ['files']},
        {transaction},
      );
      const address: Partial<Address> = {};
      if (city) address.city = city;
      if (state) address.state = state;
      await this.addressRepository.updateAll(
        address,
        {id: updatedTenant.addressId},
        {transaction},
      );
      if (selectedFiles?.length) {
        await this.fileRepository.deleteAllHard({tenantId: id}, {transaction});
        await Promise.all(
          selectedFiles.map(async file => {
            await this.fileRepository.create(
              {
                fileKey: file.fileKey,
                originalName: file.originalName,
                tenantId: id,
                source: file.source,
                size: file.size,
              },
              {transaction},
            );
          }),
        );
      }

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
}
