import {
  inject,
  Interceptor,
  InvocationContext,
  Provider,
  Setter,
  ValueOrPromise,
} from '@loopback/core';

import {HttpErrors, RequestContext} from '@loopback/rest';
import {LOGGER, ILogger, IAuthUserWithPermissions} from '@sourceloop/core';

import {AuthenticationBindings} from 'loopback4-authentication';

import * as crypto from 'crypto';
import {SYSTEM_USER} from '@sourceloop/ctrl-plane-tenant-management-service';

/**
 * Provider that returns an interceptor to verify Stripe webhook requests.
 *
 * @remarks
 * - Validates the Stripe signature header and optionally constructs the Stripe event
 *   using the webhook secret when webhook validation is enabled.
 * - On successful verification, it sets the current user to a system user so downstream
 *   handlers have an authenticated context.
 * - Throws `HttpErrors.Unauthorized` when verification fails or configuration is missing.
 */
export class ApiKeyInterceptor implements Provider<Interceptor> {
  /**
   * Creates an instance of StripeWebhookVerifierProvider.
   *
   * @param logger - Logger instance for logging errors and info
   * @param systemUser - System user to be set on the request context after successful verification
   * @param setCurrentUser - Setter function to assign the current user in the authentication binding
   */
  constructor(
    @inject(LOGGER.LOGGER_INJECT)
    private readonly logger: ILogger,
    @inject(SYSTEM_USER)
    private readonly systemUser: IAuthUserWithPermissions,
    @inject.setter(AuthenticationBindings.CURRENT_USER)
    private readonly setCurrentUser: Setter<IAuthUserWithPermissions>,
  ) {}

  /**
   * Returns the interceptor function bound to this provider.
   *
   * @returns The interceptor function to be used by LoopBack's interception system.
   */
  value() {
    return this.intercept.bind(this);
  }

  /**
   * Interceptor that validates incoming Stripe webhook requests.
   *
   * @typeParam T - The return type of the intercepted invocation.
   * @param invocationCtx - The invocation context which includes the parent RequestContext.
   * @param next - The next function in the invocation chain.
   * @returns The result of the next invocation if verification succeeds.
   *
   * @throws {HttpErrors.Unauthorized} When Stripe secret is not configured or
   * verification fails.
   */
  async intercept<T>(
    invocationCtx: InvocationContext,
    next: () => ValueOrPromise<T>,
  ) {
    const {request} = invocationCtx.parent as RequestContext; // NOSONAR
    const SECRET_KEY = process.env.SECRET_KEY ?? ''; // 32 bytes (AES-256)
    const expectedApiKey = process.env.AUTH_API_KEY ?? '';

    const encryptedApiKey = request.headers['x-api-key'];

    console.log('Encrypted API Key:', encryptedApiKey); //NOSONAR
    if (!encryptedApiKey || typeof encryptedApiKey !== 'string') {
      throw new HttpErrors.Unauthorized('Missing or invalid x-api-key header');
    }

    const [ivHex, encryptedApiKey1] = encryptedApiKey.split(':');
    const iv = Buffer.from(ivHex, 'hex');
    console.log('IV----', iv); //NOSONAR
    const decipher = crypto.createDecipheriv(
      'aes-256-cbc',
      Buffer.from(SECRET_KEY, 'base64'),
      iv,
    );
    let decryptedApiKey: string = decipher.update(
      encryptedApiKey1,
      'hex',
      'utf-8',
    );
    decryptedApiKey += decipher.final('utf-8');
    console.log('Decrypted API Key....', decryptedApiKey); //NOSONAR
    if (decryptedApiKey !== expectedApiKey) {
      throw new HttpErrors.Unauthorized('API key mismatch');
    }
    this.setCurrentUser(this.systemUser);
    return next();
  }
  catch(err: Error) {
    // Log error for audit, but do not expose details to client
    console.error('API key decryption failed:', err); // NOSONAR
    throw new HttpErrors.Unauthorized('Failed to decrypt API key');
  }
}
