// Acceptance tests for LeadController

import {Client, expect} from '@loopback/testlab';
import {TenantMgmtServiceApplication} from '../..';
import {getRepo, getToken, setupApplication} from './test-helper';
import {STATUS_CODE} from '@sourceloop/core';
import {LeadRepository} from '../../repositories';
import {Lead} from '../../models';
import {PermissionKey, LeadStatus} from '@local/core';

const mockLead = {
  firstName: 'John',
  lastName: 'Doe',
  companyName: 'Acme Corp',
  email: '<EMAIL>',
  status: LeadStatus.PENDING,
  // Optional fields:
  designation: 'Manager',
  phoneNumber: '1234567890',
  countryCode: '+91',
};

describe('LeadController', () => {
  let app: TenantMgmtServiceApplication;
  let client: Client;
  let leadRepo: LeadRepository;

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
    leadRepo = await getRepo(app, 'repositories.LeadRepository');
  });

  after(async () => {
    await app.stop();
  });

  beforeEach(async () => {
    await leadRepo.deleteAll();
  });

  it('invokes POST /leads with valid token', async () => {
    const token = getToken([PermissionKey.CreateLead]);
    const {body} = await client
      .post('/leads')
      .set('Authorization', token)
      .send(mockLead)
      .expect(STATUS_CODE.OK);
    expect(body.id).to.be.String();
    expect(body.firstName).to.eql(mockLead.firstName);
    expect(body.status).to.eql(mockLead.status);
  });

  it('invokes GET /leads/count and returns the count of leads', async () => {
    const token = getToken([PermissionKey.ViewLead]);
    await leadRepo.create({...mockLead});
    const {body} = await client
      .get('/leads/count')
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);
    expect(body.count).to.be.a.Number();
    expect(body.count).to.be.greaterThan(0);
  });

  it('invokes GET /leads and returns an array of lead instances', async () => {
    const token = getToken([PermissionKey.ViewLead]);
    await leadRepo.create({...mockLead});
    const {body} = await client
      .get('/leads')
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);
    expect(body).to.be.an.Array();
    body.forEach((lead: Partial<Lead>) => {
      expect(lead).to.have.properties(['id', 'firstName', 'status']);
    });
  });

  it('invokes GET /leads/{id} and returns a lead instance', async () => {
    const token = getToken([PermissionKey.ViewLead]);
    const createdLead = await leadRepo.create({...mockLead});
    const {body} = await client
      .get(`/leads/${createdLead.id}`)
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);
    expect(body.id).to.eql(createdLead.id);
    expect(body.firstName).to.eql(mockLead.firstName);
    expect(body.status).to.eql(mockLead.status);
  });

  it('PATCH /leads/{id} updates a lead with status PENDING and returns 204, and persists the change', async () => {
    const token = getToken([PermissionKey.UpdateLead]);
    const createdLead = await leadRepo.create({...mockLead});
    await client
      .patch(`/leads/${createdLead.id}`)
      .set('Authorization', token)
      .send({designation: 'Director'})
      .expect(204);

    const updatedLead = await leadRepo.findById(createdLead.id);
    expect(updatedLead.designation).to.eql('Director');
  });

  it('PATCH /leads/{id} returns 400 Bad Request for CONVERTED status', async () => {
    const token = getToken([PermissionKey.UpdateLead]);
    const convertedLead = await leadRepo.create({
      ...mockLead,
      status: LeadStatus.CONVERTED,
    });
    await client
      .patch(`/leads/${convertedLead.id}`)
      .set('Authorization', token)
      .send({designation: 'Director'})
      .expect(400);
  });

  it('PATCH /leads/{id} returns 400 Bad Request for INVALID status', async () => {
    const token = getToken([PermissionKey.UpdateLead]);
    const invalidLead = await leadRepo.create({
      ...mockLead,
      status: LeadStatus.INVALID,
    });
    await client
      .patch(`/leads/${invalidLead.id}`)
      .set('Authorization', token)
      .send({designation: 'Director'})
      .expect(400);
  });

  it('invokes GET /leads/all-status and returns all possible lead statuses', async () => {
    const token = getToken([PermissionKey.ViewAllStatuses]);
    const {body} = await client
      .get('/leads/all-status')
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);
    expect(body.statuses).to.be.an.Object();
    // Optionally check for specific statuses
    [LeadStatus.PENDING, LeadStatus.CONVERTED, LeadStatus.INVALID].forEach(
      key => {
        expect(body.statuses).to.have.property(String(key));
      },
    );
  });
});
