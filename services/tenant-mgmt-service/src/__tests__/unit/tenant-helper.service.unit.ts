import {expect, sinon} from '@loopback/testlab';
import {TenantHelperService} from '../../services/tenant-helper.service';
import {TenantRepository, FileRepository} from '../../repositories';
import {
  ContactRepository,
  AddressRepository,
  Address,
} from '@sourceloop/ctrl-plane-tenant-management-service';
import {Contact, FileObject, Tenant, UpdateTenantDto} from '../../models';
import {StorageSource} from '@local/core';
import {Transaction} from '../fixtures';

describe('TenantHelperService (unit)', () => {
  let tenantRepo: sinon.SinonStubbedInstance<TenantRepository<Tenant>>;
  let contactRepo: sinon.SinonStubbedInstance<ContactRepository<Contact>>;
  let addressRepo: sinon.SinonStubbedInstance<AddressRepository<Address>>;
  let fileRepo: sinon.SinonStubbedInstance<FileRepository>;
  let service: TenantHelperService;
  let transaction: {
    commit: sinon.SinonStub;
    rollback: sinon.SinonStub;
  };

  beforeEach(() => {
    tenantRepo = sinon.createStubInstance(TenantRepository);
    contactRepo = sinon.createStubInstance(ContactRepository);
    addressRepo = sinon.createStubInstance(AddressRepository);
    fileRepo = sinon.createStubInstance(FileRepository);

    transaction = {
      commit: sinon.stub(),
      rollback: sinon.stub(),
    };

    tenantRepo.beginTransaction.resolves(transaction as unknown as Transaction);
    tenantRepo.findById.resolves({
      id: 't1',
      addressId: 'addr1',
      files: [],
    } as unknown as Tenant);

    service = new TenantHelperService(
      tenantRepo as TenantRepository,
      contactRepo as ContactRepository<Contact>,
      addressRepo as AddressRepository,
      fileRepo as FileRepository,
    );
  });

  it('updates tenant, contact, address, and files (happy path)', async () => {
    const dto: Partial<UpdateTenantDto> = {
      contact: {
        firstName: 'John',
        lastName: '',
        email: '',
        countryCode: '',
        isPrimary: false,
      } as Contact,
      city: 'NY',
      state: 'NY',
      selectedFiles: [
        {
          fileKey: 'fk1',
          originalName: 'doc.pdf',
          source: StorageSource.S3,
          size: 123,
        } as FileObject,
      ],
    };

    await service.updateTenant('t1', dto);

    sinon.assert.calledWithMatch(contactRepo.updateAll, dto.contact, {
      tenantId: 't1',
    });
    sinon.assert.calledWithMatch(
      addressRepo.updateAll,
      {city: 'NY', state: 'NY'},
      {id: 'addr1'},
    );
    sinon.assert.called(fileRepo.deleteAllHard);
    sinon.assert.called(fileRepo.create);
    sinon.assert.calledOnce(transaction.commit);
  });

  it('commits transaction even if no contact/files provided', async () => {
    const dto: Partial<UpdateTenantDto> = {city: 'Delhi'};

    await service.updateTenant('t1', dto);

    sinon.assert.notCalled(contactRepo.updateAll);
    sinon.assert.notCalled(fileRepo.create);
    sinon.assert.calledOnce(transaction.commit);
  });

  it('rolls back transaction on error', async () => {
    tenantRepo.updateById.rejects(new Error('DB error'));
    const dto: Partial<UpdateTenantDto> = {city: 'Delhi'};

    await expect(service.updateTenant('t1', dto)).to.be.rejectedWith(
      'DB error',
    );
    sinon.assert.calledOnce(transaction.rollback);
  });
});
