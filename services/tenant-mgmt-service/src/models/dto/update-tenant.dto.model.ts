import {model, property} from '@loopback/repository';
import {FileObject} from './file-dto.model';
import {Tenant} from '../tenant.model';
import {Contact} from '../contact.model';
import {getJsonSchema} from '@loopback/openapi-v3';

/**
 * DTO (Data Transfer Object) used to update and onboard a tenant.
 *
 * Extends the base {@link Tenant} model and adds fields that are
 * specifically required when updating tenant details, such as
 * selected files, contact metadata, and location details.
 */
@model({
  description: 'model describing payload used to create and onboard a tenant',
})
export class UpdateTenantDto extends Tenant {
  /**
   * Files associated with the tenant (e.g., documents, images, or
   * supporting files).
   *
   * @type {FileObject[]}
   * @required
   */
  @property({
    type: 'array',
    itemType: 'object',
    name: 'files',
    required: true,
  })
  selectedFiles: FileObject[];

  /**
   * Metadata for the tenant’s primary contact person.
   *
   * Excludes the `id` and `tenantId` fields from the base {@link Contact} model.
   *
   * @type {Omit<Contact, 'id' | 'tenantId'>}
   */
  @property({
    type: 'object',
    description: 'metadata for the contact to be updated.',
    jsonSchema: getJsonSchema(Contact, {
      exclude: ['tenantId', 'id'],
    }),
  })
  contact: Omit<Contact, 'id' | 'tenantId'>;

  /**
   * City of the tenant admin.
   *
   * @type {string | undefined}
   */
  @property({
    type: 'string',
    description: 'city of the tenant admin',
  })
  city?: string;

  /**
   * State of the tenant admin.
   *
   * @type {string | undefined}
   */
  @property({
    description: 'state of the tenant admin',
    type: 'string',
  })
  state?: string;

  /**
   * Constructs a new instance of {@link UpdateTenantDto}.
   *
   * @param {Partial<UpdateTenantDto>} [data] - Optional partial data used to initialize the DTO.
   */
  constructor(data?: Partial<UpdateTenantDto>) {
    super(data);
  }
}
