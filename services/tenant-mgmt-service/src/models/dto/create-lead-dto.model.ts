import {model, property} from '@loopback/repository';
import {Lead} from '../lead.model';

@model({
  description: 'model describing payload used to create a lead',
})
export class CreateLeadDTO extends Lead {
  @property({
    type: 'string',
    description: 'address of the tenant owners',
  })
  address?: string;

  @property({
    type: 'string',
    description: 'city of the tenant owner',
  })
  city?: string;

  @property({
    description: 'state of the tenant owner',
    type: 'string',
  })
  state?: string;

  @property({
    description: 'zip code of the tenant owner',
    type: 'string',
  })
  zip?: string;

  @property({
    type: 'string',
    description: 'country of the tenant owner',
  })
  country?: string;

  constructor(data?: Partial<CreateLeadDTO>) {
    super(data);
  }
}
