{"openapi": "3.0.0", "info": {"title": "tenant-mgmt-service", "version": "1.0.0", "description": "tenant-mgmt-service", "contact": {}}, "paths": {"/contacts/count": {"get": {"x-controller-name": "ContactController", "x-operation-name": "count", "tags": ["ContactController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Contact model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10211   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "contacts.Where<PERSON><PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Contact>"}}}}], "operationId": "ContactController.count"}}, "/contacts/{id}": {"put": {"x-controller-name": "ContactController", "x-operation-name": "replaceById", "tags": ["ContactController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Contact PUT success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 10209   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Contact"}}}, "x-parameter-index": 1}, "operationId": "ContactController.replaceById"}, "patch": {"x-controller-name": "ContactController", "x-operation-name": "updateById", "tags": ["ContactController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Contact PATCH success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Contact"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10209   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContactPartial"}}}, "x-parameter-index": 1}, "operationId": "ContactController.updateById"}, "get": {"x-controller-name": "ContactController", "x-operation-name": "findById", "tags": ["ContactController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Contact model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Contact"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10211   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/contacts.Filter"}}}}], "operationId": "ContactController.findById"}, "delete": {"x-controller-name": "ContactController", "x-operation-name": "deleteById", "tags": ["ContactController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Contact DELETE success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 10210   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "ContactController.deleteById"}}, "/contacts": {"post": {"x-controller-name": "ContactController", "x-operation-name": "create", "tags": ["ContactController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Contact model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Contact"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10208   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewContact"}}}}, "operationId": "ContactController.create"}, "patch": {"x-controller-name": "ContactController", "x-operation-name": "updateAll", "tags": ["ContactController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Contact PATCH success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Contact"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10209   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "contacts.Where<PERSON><PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Contact>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContactPartial"}}}}, "operationId": "ContactController.updateAll"}, "get": {"x-controller-name": "ContactController", "x-operation-name": "find", "tags": ["ContactController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Contact model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ContactWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10211   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/contacts.Filter1"}}}}], "operationId": "ContactController.find"}}, "/invoices/count": {"get": {"x-controller-name": "InvoiceController", "x-operation-name": "count", "tags": ["InvoiceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Invoice model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10215   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "invoices.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Invoice>"}}}}], "operationId": "InvoiceController.count"}}, "/invoices/download": {"get": {"x-controller-name": "InvoiceController", "x-operation-name": "downloadInvoice", "tags": ["InvoiceController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Invoice download success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 10212   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}], "operationId": "InvoiceController.downloadInvoice"}}, "/invoices/{id}": {"put": {"x-controller-name": "InvoiceController", "x-operation-name": "replaceById", "tags": ["InvoiceController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Invoice PUT success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 10213   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Invoice"}}}, "x-parameter-index": 1}, "operationId": "InvoiceController.replaceById"}, "patch": {"x-controller-name": "InvoiceController", "x-operation-name": "updateById", "tags": ["InvoiceController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Invoice PATCH success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Invoice"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10213   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoicePartial"}}}, "x-parameter-index": 1}, "operationId": "InvoiceController.updateById"}, "get": {"x-controller-name": "InvoiceController", "x-operation-name": "findById", "tags": ["InvoiceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Invoice model instance success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Invoice"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10215   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/invoices.Filter"}}}}], "operationId": "InvoiceController.findById"}, "delete": {"x-controller-name": "InvoiceController", "x-operation-name": "deleteById", "tags": ["InvoiceController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Invoice DELETE success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 10214   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "InvoiceController.deleteById"}}, "/invoices": {"post": {"x-controller-name": "InvoiceController", "x-operation-name": "create", "tags": ["InvoiceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Invoice model instance POST success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Invoice"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10212   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewInvoice"}}}}, "operationId": "InvoiceController.create"}, "patch": {"x-controller-name": "InvoiceController", "x-operation-name": "updateAll", "tags": ["InvoiceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Invoice PATCH success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Invoice"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10213   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "invoices.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Invoice>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoicePartial"}}}}, "operationId": "InvoiceController.updateAll"}, "get": {"x-controller-name": "InvoiceController", "x-operation-name": "find", "tags": ["InvoiceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Invoice model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/InvoiceWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10215   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/invoices.Filter1"}}}}], "operationId": "InvoiceController.find"}}, "/leads/all-status": {"get": {"x-controller-name": "LeadController", "x-operation-name": "findAllStatus", "tags": ["LeadController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Object of all possible lead status", "permissions": ["10216"], "content": {"application/json": {"schema": {"type": "object", "items": {}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10216   |\n", "operationId": "LeadController.findAllStatus"}}, "/leads/count": {"get": {"x-controller-name": "LeadController", "x-operation-name": "count", "tags": ["LeadController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Lead model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10203   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "leads.<PERSON><PERSON><PERSON><PERSON>", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Lead>"}}}}], "operationId": "LeadController.count"}}, "/leads/{id}/tenants": {"post": {"x-controller-name": "LeadTenantController", "x-operation-name": "tenantFromLead", "tags": ["LeadTenantController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Tenant model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Tenant"}}}}}, "description": "", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantOnboardDto"}}}}, "operationId": "LeadTenantController.tenantFromLead"}}, "/leads/{id}": {"patch": {"x-controller-name": "LeadController", "x-operation-name": "updateById", "tags": ["LeadController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Lead PATCH success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Lead"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10201   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LeadPartial"}}}, "x-parameter-index": 1}, "operationId": "LeadController.updateById"}, "get": {"x-controller-name": "LeadController", "x-operation-name": "findById", "tags": ["LeadController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Lead model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Lead"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10203   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/leads.Filter"}}}}], "operationId": "LeadController.findById"}}, "/leads": {"post": {"x-controller-name": "LeadController", "x-operation-name": "create", "tags": ["LeadController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Lead model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Lead"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10200   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateLeadDTO"}}}}, "operationId": "LeadController.create"}, "get": {"x-controller-name": "LeadController", "x-operation-name": "find", "tags": ["LeadController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Lead model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LeadWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10203   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/leads.Filter1"}}}}], "operationId": "LeadController.find"}}, "/ping": {"get": {"x-controller-name": "PingController", "x-operation-name": "ping", "tags": ["PingController"], "responses": {"200": {"description": "Ping Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PingResponse"}}}}}, "description": "", "operationId": "PingController.ping"}}, "/tenant-configs/count": {"get": {"x-controller-name": "TenantMgmtConfigController", "x-operation-name": "count", "tags": ["TenantMgmtConfigController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Tenant Config model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10221   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "tenant_mgmt_configs.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<TenantMgmtConfig>"}}}}], "operationId": "TenantMgmtConfigController.count"}}, "/tenant-configs/{id}/tenant": {"get": {"x-controller-name": "TenantMgmtConfigTenantController", "x-operation-name": "<PERSON><PERSON><PERSON><PERSON>", "tags": ["TenantMgmtConfigTenantController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Tenant belonging to TenantConfig", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Tenant"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10221   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "TenantMgmtConfigTenantController.getTenant"}}, "/tenant-configs/{id}": {"put": {"x-controller-name": "TenantMgmtConfigController", "x-operation-name": "replaceById", "tags": ["TenantMgmtConfigController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Tenant Config PUT success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 10222   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantMgmtConfig"}}}, "x-parameter-index": 1}, "operationId": "TenantMgmtConfigController.replaceById"}, "patch": {"x-controller-name": "TenantMgmtConfigController", "x-operation-name": "updateById", "tags": ["TenantMgmtConfigController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Tenant Config PATCH success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantMgmtConfig"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10222   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantMgmtConfigPartial"}}}, "x-parameter-index": 1}, "operationId": "TenantMgmtConfigController.updateById"}, "get": {"x-controller-name": "TenantMgmtConfigController", "x-operation-name": "findById", "tags": ["TenantMgmtConfigController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Tenant Config model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantMgmtConfig"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10221   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/tenant_mgmt_configs.Filter"}}}}], "operationId": "TenantMgmtConfigController.findById"}, "delete": {"x-controller-name": "TenantMgmtConfigController", "x-operation-name": "deleteById", "tags": ["TenantMgmtConfigController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Tenant DELETE success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 10223   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "TenantMgmtConfigController.deleteById"}}, "/tenant-configs": {"post": {"x-controller-name": "TenantMgmtConfigController", "x-operation-name": "create", "tags": ["TenantMgmtConfigController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Tenant Config model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantMgmtConfig"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10220   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewTenantConfig"}}}}, "operationId": "TenantMgmtConfigController.create"}, "patch": {"x-controller-name": "TenantMgmtConfigController", "x-operation-name": "updateAll", "tags": ["TenantMgmtConfigController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Tenant Config PATCH success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantMgmtConfig"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10222   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "tenant_mgmt_configs.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<TenantMgmtConfig>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantMgmtConfigPartial"}}}}, "operationId": "TenantMgmtConfigController.updateAll"}, "get": {"x-controller-name": "TenantMgmtConfigController", "x-operation-name": "find", "tags": ["TenantMgmtConfigController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of TenantConfig model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TenantMgmtConfigWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10221   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/tenant_mgmt_configs.Filter1"}}}}], "operationId": "TenantMgmtConfigController.find"}}, "/tenants/all-status/metrics": {"get": {"x-controller-name": "TenantController", "x-operation-name": "findAllStatusMetrics", "tags": ["TenantController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Metrics of all the statues", "permissions": ["10216"], "content": {"application/json": {"schema": {"type": "object"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10216   |\n", "operationId": "TenantController.findAllStatusMetrics"}}, "/tenants/all-status": {"get": {"x-controller-name": "TenantController", "x-operation-name": "findAllStatus", "tags": ["TenantController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Object of all possible tenant status", "permissions": ["10216"], "content": {"application/json": {"schema": {"type": "object", "items": {}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10216   |\n", "operationId": "TenantController.findAllStatus"}}, "/tenants/count": {"get": {"x-controller-name": "TenantController", "x-operation-name": "count", "tags": ["TenantController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Tenant model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10207   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "tenants.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Tenant>"}}}}], "operationId": "TenantController.count"}}, "/tenants/update-status": {"post": {"x-controller-name": "TenantController", "x-operation-name": "updateStatus", "tags": ["TenantController"], "responses": {"200": {"description": "Tenant status updated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Tenant"}}}}}, "description": "", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "status": {"type": "string"}}, "required": ["id", "status"]}}}}, "operationId": "TenantController.updateStatus"}}, "/tenants/verify-key": {"post": {"x-controller-name": "TenantController", "x-operation-name": "<PERSON><PERSON><PERSON>", "tags": ["TenantController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "couples of possible leads if not requested key is not available", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KeySuggestionDto"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10207   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyKeyDto"}}}}, "operationId": "TenantController.verifyKey"}}, "/tenants/{id}/provision": {"post": {"x-controller-name": "TenantController", "x-operation-name": "provision", "tags": ["TenantController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Provisioning success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 10216   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionDTO"}}}}, "operationId": "TenantController.provision"}}, "/tenants/{id}": {"patch": {"x-controller-name": "TenantController", "x-operation-name": "updateById", "tags": ["TenantController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Tenant PATCH success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Tenant"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10205   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTenantDtoPartial"}}}, "x-parameter-index": 1}, "operationId": "TenantController.updateById"}, "get": {"x-controller-name": "TenantController", "x-operation-name": "findById", "tags": ["TenantController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Tenant model instance with contact details, files, and address", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantWithRelations"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10207   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "TenantController.findById"}}, "/tenants": {"post": {"x-controller-name": "TenantController", "x-operation-name": "create", "tags": ["TenantController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Tenant model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Tenant"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10204   |\n", "parameters": [{"name": "leadId", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewTenantOnboarding"}}}}, "operationId": "TenantController.create"}, "get": {"x-controller-name": "TenantController", "x-operation-name": "find", "tags": ["TenantController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Tenant model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TenantWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10207   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/tenants.Filter"}}}}], "operationId": "TenantController.find"}}, "/": {"get": {"x-controller-name": "HomePageController", "x-operation-name": "homePage", "tags": ["HomePageController"], "responses": {"200": {"description": "Home Page", "content": {"text/html": {"schema": {"type": "string"}}}}}, "description": "", "operationId": "HomePageController.homePage"}}}, "components": {"securitySchemes": {"HTTPBearer": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"Contact": {"title": "Contact", "type": "object", "description": "contacts belonging to a tenant", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "firstName": {"type": "string", "description": "first name of the lead"}, "lastName": {"type": "string", "description": "last name of the lead"}, "email": {"type": "string", "description": "email id of the contact"}, "isPrimary": {"type": "boolean", "description": "boolean value denoting if the contact is a primary contact for it's tenant."}, "type": {"type": "string", "description": "type of the contact"}, "tenantId": {"type": "string", "description": "tenant id this contact belongs to"}}, "required": ["firstName", "lastName", "email", "isPrimary"], "additionalProperties": false}, "NewContact": {"title": "NewContact", "type": "object", "description": "contacts belonging to a tenant (tsType: Omit<Contact, 'id'>, schemaOptions: { title: 'NewContact', exclude: [ 'id' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "firstName": {"type": "string", "description": "first name of the lead"}, "lastName": {"type": "string", "description": "last name of the lead"}, "email": {"type": "string", "description": "email id of the contact"}, "isPrimary": {"type": "boolean", "description": "boolean value denoting if the contact is a primary contact for it's tenant."}, "type": {"type": "string", "description": "type of the contact"}, "tenantId": {"type": "string", "description": "tenant id this contact belongs to"}}, "required": ["firstName", "lastName", "email", "isPrimary"], "additionalProperties": false}, "ResourceWithRelations": {"title": "ResourceWithRelations", "type": "object", "description": "model for resources that are provisioned for a tenant (tsType: ResourceWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "externalIdentifier": {"type": "string", "description": "identifier for the resource in the external system it was provisioned"}, "type": {"type": "string", "description": "type of the resource like storage, compute, etc"}, "metadata": {"type": "object", "description": "any type specific metadata of the resource like connection info, size, etc"}, "tenantId": {"type": "string", "description": "id of the tenant for which this resource is provisioned"}, "tenant": {"$ref": "#/components/schemas/TenantWithRelations"}, "foreignKey": {}}, "required": ["externalIdentifier", "type", "metadata"], "additionalProperties": false}, "AddressWithRelations": {"title": "AddressWithRelations", "type": "object", "description": "this model represents the address of a company or lead (tsType: AddressWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "address": {"type": "string", "description": "address of the company"}, "city": {"type": "string", "description": "city of the company"}, "state": {"type": "string", "description": "state of the company"}, "zip": {"type": "string", "description": "zip code of the company"}, "country": {"type": "string", "description": "country of the company"}}, "required": ["country"], "additionalProperties": false}, "LeadWithRelations": {"title": "LeadWithRelations", "type": "object", "description": "this model represents a lead that could eventually be a tenant in the system (tsType: LeadWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "firstName": {"type": "string", "description": "first name of the lead"}, "lastName": {"type": "string", "description": "last name of the lead"}, "companyName": {"type": "string", "description": "name of the lead's company"}, "email": {"type": "string", "description": "email of the lead"}, "isValidated": {"type": "boolean", "description": "whether the lead`s email has been validated or not"}, "addressId": {"type": "string", "description": "id of the address of the lead"}, "tenant": {"$ref": "#/components/schemas/TenantWithRelations"}, "address": {"$ref": "#/components/schemas/AddressWithRelations"}, "foreignKey": {}}, "required": ["firstName", "lastName", "companyName", "email", "isValidated"], "additionalProperties": false}, "TenantWithRelations": {"title": "TenantWithRelations", "type": "object", "description": "main model of the service that represents a tenant in the system, either pooled or siloed (tsType: TenantWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string", "description": "name of the tenant"}, "status": {"type": "number", "description": "status of a tenant, it can be - 0(active), 1(provisioning),2(deprovisioning),3(inactive)", "enum": [0, 1, 2, 3, 4, 5]}, "key": {"type": "string", "description": "a short string used to identify a tenant. This is also used as the namespace and subdomain for this particular tenant", "pattern": "^[a-z0-9]+$", "maxLength": 10}, "spocUserId": {"type": "string", "description": "user id of the admin user who acts as a spoc for this tenant."}, "domains": {"type": "array", "items": {"type": "string", "description": "array of domains that are allowed for this tenant"}}, "leadId": {"type": "string", "description": "id of the lead from which this tenant was generated. this is optional as a tenant can be created without this lead."}, "addressId": {"type": "string", "description": "id of the address of the tenant"}, "contacts": {"type": "array", "items": {"$ref": "#/components/schemas/ContactWithRelations"}}, "resources": {"type": "array", "items": {"$ref": "#/components/schemas/ResourceWithRelations"}}, "lead": {"$ref": "#/components/schemas/LeadWithRelations"}, "foreignKey": {}, "address": {"$ref": "#/components/schemas/AddressWithRelations"}}, "required": ["name", "status", "key", "domains"], "additionalProperties": false}, "ContactWithRelations": {"title": "ContactWithRelations", "type": "object", "description": "main model of the service that represents a tenant in the system, either pooled or siloed (tsType: ContactWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "firstName": {"type": "string", "description": "first name of the tenant admin", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "lastName": {"type": "string", "description": "last name of the tenant admin", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "userName": {"type": "string", "description": "user name of the tenant admin", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z0-9]+$"}, "password": {"type": "string", "description": "password of the tenant admin"}, "email": {"type": "string", "description": "email id of the contact", "format": "email", "pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", "errorMessage": {"pattern": "Invalid email format. Please provide a valid email address."}}, "designation": {"type": "string", "nullable": true, "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "phoneNumber": {"type": "string", "nullable": true, "minLength": 10, "maxLength": 10, "pattern": "^\\d{10}$", "errorMessage": {"pattern": "Invalid phone number format. Use only digits.", "minLength": "Phone number must be at least 10 digits long.", "maxLength": "Phone number cannot exceed 10 characters."}}, "countryCode": {"type": "string", "minLength": 2, "maxLength": 5, "pattern": "^\\+[0-9]{1,4}$", "errorMessage": {"pattern": "Invalid country code format. It should start with an + followed by 1 to 4 digits.", "minLength": "Country code must be at least 2 characters.", "maxLength": "Country code cannot exceed 5 characters."}}, "isPrimary": {"type": "boolean", "description": "boolean value denoting if the contact is a primary contact for it's tenant."}, "type": {"type": "string", "description": "type of the contact"}, "tenantId": {"type": "string", "description": "tenant id this contact belongs to"}, "tenant": {"$ref": "#/components/schemas/TenantWithRelations"}, "foreignKey": {}}, "required": ["firstName", "lastName", "userName", "email", "isPrimary"], "additionalProperties": false}, "ContactPartial": {"title": "ContactPartial", "type": "object", "description": "contacts belonging to a tenant (tsType: Partial<Contact>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "firstName": {"type": "string", "description": "first name of the lead"}, "lastName": {"type": "string", "description": "last name of the lead"}, "email": {"type": "string", "description": "email id of the contact"}, "isPrimary": {"type": "boolean", "description": "boolean value denoting if the contact is a primary contact for it's tenant."}, "type": {"type": "string", "description": "type of the contact"}, "tenantId": {"type": "string", "description": "tenant id this contact belongs to"}}, "additionalProperties": false}, "Invoice": {"title": "Invoice", "type": "object", "description": "this model represents an invoice with the amount and period generated for a tenant in the system", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "startDate": {"type": "string", "description": "start date for the period this invoice is generated for"}, "endDate": {"type": "string", "description": "end date for the period this invoice is generated for"}, "amount": {"type": "number", "description": "total amount for the invoice"}, "currencyCode": {"type": "string", "description": "currency for the invoice"}, "invoiceFile": {"type": "string", "description": "option reference to the generated file of the invoice"}, "dueDate": {"type": "string", "description": "due date for the invoice"}, "status": {"type": "number", "description": "status of the invoice - 0(PENDING), 1(PAID), 2(CANCELLED)", "enum": ["PENDING", "PAID", "CANCELLED", 0, 1, 2]}, "tenantId": {"type": "string", "description": "id of the tenant this invoice is generated for"}}, "required": ["startDate", "endDate", "amount", "currencyCode", "dueDate", "status", "tenantId"], "additionalProperties": false}, "NewInvoice": {"title": "NewInvoice", "type": "object", "description": "this model represents an invoice with the amount and period generated for a tenant in the system (tsType: Omit<Invoice, 'id'>, schemaOptions: { title: 'NewInvoice', exclude: [ 'id' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "startDate": {"type": "string", "description": "start date for the period this invoice is generated for"}, "endDate": {"type": "string", "description": "end date for the period this invoice is generated for"}, "amount": {"type": "number", "description": "total amount for the invoice"}, "currencyCode": {"type": "string", "description": "currency for the invoice"}, "invoiceFile": {"type": "string", "description": "option reference to the generated file of the invoice"}, "dueDate": {"type": "string", "description": "due date for the invoice"}, "status": {"type": "number", "description": "status of the invoice - 0(PENDING), 1(PAID), 2(CANCELLED)", "enum": ["PENDING", "PAID", "CANCELLED", 0, 1, 2]}, "tenantId": {"type": "string", "description": "id of the tenant this invoice is generated for"}}, "required": ["startDate", "endDate", "amount", "currencyCode", "dueDate", "status", "tenantId"], "additionalProperties": false}, "InvoiceWithRelations": {"title": "InvoiceWithRelations", "type": "object", "description": "this model represents an invoice with the amount and period generated for a tenant in the system (tsType: InvoiceWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "startDate": {"type": "string", "description": "start date for the period this invoice is generated for"}, "endDate": {"type": "string", "description": "end date for the period this invoice is generated for"}, "amount": {"type": "number", "description": "total amount for the invoice"}, "currencyCode": {"type": "string", "description": "currency for the invoice"}, "invoiceFile": {"type": "string", "description": "option reference to the generated file of the invoice"}, "dueDate": {"type": "string", "description": "due date for the invoice"}, "status": {"type": "number", "description": "status of the invoice - 0(PENDING), 1(PAID), 2(CANCELLED)", "enum": ["PENDING", "PAID", "CANCELLED", 0, 1, 2]}, "tenantId": {"type": "string", "description": "id of the tenant this invoice is generated for"}, "tenant": {"$ref": "#/components/schemas/TenantWithRelations"}, "foreignKey": {}}, "required": ["startDate", "endDate", "amount", "currencyCode", "dueDate", "status", "tenantId"], "additionalProperties": false}, "InvoicePartial": {"title": "InvoicePartial", "type": "object", "description": "this model represents an invoice with the amount and period generated for a tenant in the system (tsType: Partial<Invoice>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "startDate": {"type": "string", "description": "start date for the period this invoice is generated for"}, "endDate": {"type": "string", "description": "end date for the period this invoice is generated for"}, "amount": {"type": "number", "description": "total amount for the invoice"}, "currencyCode": {"type": "string", "description": "currency for the invoice"}, "invoiceFile": {"type": "string", "description": "option reference to the generated file of the invoice"}, "dueDate": {"type": "string", "description": "due date for the invoice"}, "status": {"type": "number", "description": "status of the invoice - 0(PENDING), 1(PAID), 2(CANCELLED)", "enum": ["PENDING", "PAID", "CANCELLED", 0, 1, 2]}, "tenantId": {"type": "string", "description": "id of the tenant this invoice is generated for"}}, "additionalProperties": false}, "Tenant": {"title": "Tenant", "type": "object", "description": "main model of the service that represents a tenant in the system, either pooled or siloed", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string", "description": "name of the tenant"}, "status": {"type": "number", "description": "status of a tenant, it can be - 0(active), 1(provisioning),2(deprovisioning),3(inactive)", "enum": [0, 1, 2, 3, 4, 5]}, "key": {"type": "string", "description": "a short string used to identify a tenant. This is also used as the namespace and subdomain for this particular tenant", "pattern": "^[a-z0-9]+$", "maxLength": 10}, "spocUserId": {"type": "string", "description": "user id of the admin user who acts as a spoc for this tenant."}, "domains": {"type": "array", "items": {"type": "string", "description": "array of domains that are allowed for this tenant"}}, "leadId": {"type": "string", "description": "id of the lead from which this tenant was generated. this is optional as a tenant can be created without this lead."}, "addressId": {"type": "string", "description": "id of the address of the tenant"}}, "required": ["name", "status", "key", "domains"], "additionalProperties": false}, "TenantOnboardDto": {"title": "TenantOnboardDto", "type": "object", "description": "model describing payload used to create and onboard a tenant (tsType: @loopback/repository-json-schema#Optional<Omit<TenantOnboardDTO, 'contact'>, 'name'>, schemaOptions: { title: 'TenantOnboardDto', exclude: [ 'contact' ], optional: [ 'name' ] })", "properties": {"name": {"type": "string"}, "address": {"type": "string", "description": "address of the tenant owners"}, "city": {"type": "string", "description": "city of the tenant owner"}, "state": {"type": "string", "description": "state of the tenant owner"}, "zip": {"type": "string", "description": "zip code of the tenant owner"}, "country": {"type": "string", "description": "country of the tenant owner"}, "key": {"type": "string", "pattern": "^[a-z0-9]+$", "maxLength": 10}, "domains": {"type": "array", "uniqueItems": true, "items": {"type": "string", "format": "hostname"}}}, "required": ["key", "domains"], "additionalProperties": false}, "Lead": {"title": "Lead", "type": "object", "description": "this model represents a lead that could eventually be a tenant in the system", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "firstName": {"type": "string", "description": "first name of the lead"}, "lastName": {"type": "string", "description": "last name of the lead"}, "companyName": {"type": "string", "description": "name of the lead's company"}, "email": {"type": "string", "description": "email of the lead"}, "designation": {"type": "string", "description": "designation of the lead"}, "phoneNumber": {"type": "string", "description": "phone number of the lead"}, "countryCode": {"type": "string", "description": "country code of the lead"}, "status": {"type": "number", "description": "status of the lead", "enum": [0, 1, 2]}, "isValidated": {"type": "boolean", "description": "whether the lead`s email has been validated or not"}, "addressId": {"type": "string", "description": "id of the address of the lead"}}, "required": ["firstName", "lastName", "companyName", "email", "isValidated"], "additionalProperties": false}, "CreateLeadDTO": {"title": "CreateLeadDTO", "type": "object", "description": "model describing payload used to create a lead (tsType: Omit<CreateLeadDTO, 'isValidated' | 'addressId' | 'id'>, schemaOptions: { title: 'CreateLeadDTO', exclude: [ 'isValidated', 'addressId', 'id' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "firstName": {"type": "string", "description": "first name of the lead"}, "lastName": {"type": "string", "description": "last name of the lead"}, "companyName": {"type": "string", "description": "name of the lead's company"}, "email": {"type": "string", "description": "email of the lead"}, "designation": {"type": "string", "description": "designation of the lead"}, "phoneNumber": {"type": "string", "description": "phone number of the lead"}, "countryCode": {"type": "string", "description": "country code of the lead"}, "status": {"type": "number", "description": "status of the lead", "enum": [0, 1, 2]}, "address": {"type": "string", "description": "address of the tenant owners"}, "city": {"type": "string", "description": "city of the tenant owner"}, "state": {"type": "string", "description": "state of the tenant owner"}, "zip": {"type": "string", "description": "zip code of the tenant owner"}, "country": {"type": "string", "description": "country of the tenant owner"}}, "required": ["firstName", "lastName", "companyName", "email"], "additionalProperties": false}, "FileWithRelations": {"title": "FileWithRelations", "type": "object", "description": "(tsType: FileWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "tenantId": {"type": "string"}, "fileKey": {"type": "string"}, "originalName": {"type": "string"}, "source": {"type": "number"}, "size": {"type": "number"}, "tenant": {"$ref": "#/components/schemas/TenantWithRelations"}, "foreignKey": {}}, "additionalProperties": false, "x-typescript-type": "FileWithRelations"}, "LeadPartial": {"title": "LeadPartial", "type": "object", "description": "this model represents a lead that could eventually be a tenant in the system (tsType: Partial<Lead>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "firstName": {"type": "string", "description": "first name of the lead"}, "lastName": {"type": "string", "description": "last name of the lead"}, "companyName": {"type": "string", "description": "name of the lead's company"}, "email": {"type": "string", "description": "email of the lead"}, "designation": {"type": "string", "description": "designation of the lead"}, "phoneNumber": {"type": "string", "description": "phone number of the lead"}, "countryCode": {"type": "string", "description": "country code of the lead"}, "status": {"type": "number", "description": "status of the lead", "enum": [0, 1, 2]}, "isValidated": {"type": "boolean", "description": "whether the lead`s email has been validated or not"}, "addressId": {"type": "string", "description": "id of the address of the lead"}}, "additionalProperties": false}, "NewTenantOnboarding": {"title": "NewTenantOnboarding", "type": "object", "description": "model describing payload used to create and onboard a tenant (tsType: Omit<TenantOnboardDTO, >, schemaOptions: { title: 'NewTenantOnboarding', exclude: [] })", "properties": {"contact": {"type": "object", "description": "main model of the service that represents a tenant in the system, either pooled or siloed (tsType: Omit<Contact, 'tenantId' | 'id'>, schemaOptions: { exclude: [ 'tenantId', 'id' ] })", "title": "ContactExcluding_tenantId-id_", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "firstName": {"type": "string", "description": "first name of the tenant admin", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "lastName": {"type": "string", "description": "last name of the tenant admin", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "userName": {"type": "string", "description": "user name of the tenant admin", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z0-9]+$"}, "password": {"type": "string", "description": "password of the tenant admin"}, "email": {"type": "string", "description": "email id of the contact", "format": "email", "pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", "errorMessage": {"pattern": "Invalid email format. Please provide a valid email address."}}, "designation": {"type": "string", "nullable": true, "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "phoneNumber": {"type": "string", "nullable": true, "minLength": 10, "maxLength": 10, "pattern": "^\\d{10}$", "errorMessage": {"pattern": "Invalid phone number format. Use only digits.", "minLength": "Phone number must be at least 10 digits long.", "maxLength": "Phone number cannot exceed 10 characters."}}, "countryCode": {"type": "string", "minLength": 2, "maxLength": 5, "pattern": "^\\+[0-9]{1,4}$", "errorMessage": {"pattern": "Invalid country code format. It should start with an + followed by 1 to 4 digits.", "minLength": "Country code must be at least 2 characters.", "maxLength": "Country code cannot exceed 5 characters."}}, "isPrimary": {"type": "boolean", "description": "boolean value denoting if the contact is a primary contact for it's tenant."}, "type": {"type": "string", "description": "type of the contact"}}, "required": ["firstName", "lastName", "userName", "email", "isPrimary"], "additionalProperties": false}, "planId": {"type": "string", "description": "id of the plan associated with this tenant."}, "name": {"type": "string", "minLength": 3, "maxLength": 50, "pattern": "^(?![-\\s])[a-zA-Z0-9\\s&.,'’-]+(?<![-\\s])$", "errorMessage": {"pattern": "Invalid name format. Use only letters, numbers, spaces, &, ., ,, ', and –. It cannot start or end with a space or hyphen."}}, "address": {"type": "string", "description": "address of the tenant owners"}, "city": {"type": "string", "description": "city of the tenant owner"}, "state": {"type": "string", "description": "state of the tenant owner"}, "zip": {"type": "string", "description": "zip code of the tenant owner"}, "country": {"type": "string", "description": "country of the tenant owner"}, "key": {"type": "string", "description": "A short string used to identify a tenant. This is also used as the namespace and subdomain for this particular tenant.", "pattern": "^(?!-)[a-zA-Z0-9-]{3,63}(?<!-)$", "minLength": 3, "maxLength": 63}, "domains": {"type": "array", "uniqueItems": true, "items": {"type": "string", "format": "hostname"}}, "lang": {"type": "string", "enum": ["English"]}, "files": {"type": "array", "items": {"type": "object"}}, "planName": {"type": "string"}}, "required": ["name", "key", "domains", "lang"], "additionalProperties": false}, "TenantOnboardDTO": {"title": "TenantOnboardDTO", "type": "object", "description": "model describing payload used to create and onboard a tenant", "properties": {"contact": {"type": "object", "description": "main model of the service that represents a tenant in the system, either pooled or siloed (tsType: Omit<Contact, 'tenantId' | 'id'>, schemaOptions: { exclude: [ 'tenantId', 'id' ] })", "title": "ContactExcluding_tenantId-id_", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "firstName": {"type": "string", "description": "first name of the tenant admin", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "lastName": {"type": "string", "description": "last name of the tenant admin", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "userName": {"type": "string", "description": "user name of the tenant admin", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z0-9]+$"}, "password": {"type": "string", "description": "password of the tenant admin"}, "email": {"type": "string", "description": "email id of the contact", "format": "email", "pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", "errorMessage": {"pattern": "Invalid email format. Please provide a valid email address."}}, "designation": {"type": "string", "nullable": true, "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "phoneNumber": {"type": "string", "nullable": true, "minLength": 10, "maxLength": 10, "pattern": "^\\d{10}$", "errorMessage": {"pattern": "Invalid phone number format. Use only digits.", "minLength": "Phone number must be at least 10 digits long.", "maxLength": "Phone number cannot exceed 10 characters."}}, "countryCode": {"type": "string", "minLength": 2, "maxLength": 5, "pattern": "^\\+[0-9]{1,4}$", "errorMessage": {"pattern": "Invalid country code format. It should start with an + followed by 1 to 4 digits.", "minLength": "Country code must be at least 2 characters.", "maxLength": "Country code cannot exceed 5 characters."}}, "isPrimary": {"type": "boolean", "description": "boolean value denoting if the contact is a primary contact for it's tenant."}, "type": {"type": "string", "description": "type of the contact"}}, "required": ["firstName", "lastName", "userName", "email", "isPrimary"], "additionalProperties": false}, "planId": {"type": "string", "description": "id of the plan associated with this tenant."}, "name": {"type": "string", "minLength": 3, "maxLength": 50, "pattern": "^(?![-\\s])[a-zA-Z0-9\\s&.,'’-]+(?<![-\\s])$", "errorMessage": {"pattern": "Invalid name format. Use only letters, numbers, spaces, &, ., ,, ', and –. It cannot start or end with a space or hyphen."}}, "address": {"type": "string", "description": "address of the tenant owners"}, "city": {"type": "string", "description": "city of the tenant owner"}, "state": {"type": "string", "description": "state of the tenant owner"}, "zip": {"type": "string", "description": "zip code of the tenant owner"}, "country": {"type": "string", "description": "country of the tenant owner"}, "key": {"type": "string", "description": "A short string used to identify a tenant. This is also used as the namespace and subdomain for this particular tenant.", "pattern": "^(?!-)[a-zA-Z0-9-]{3,63}(?<!-)$", "minLength": 3, "maxLength": 63}, "domains": {"type": "array", "uniqueItems": true, "items": {"type": "string", "format": "hostname"}}, "lang": {"type": "string", "enum": ["English"]}, "files": {"type": "array", "items": {"type": "object"}}, "planName": {"type": "string"}}, "required": ["name", "key", "domains", "lang"], "additionalProperties": false}, "KeySuggestionDto": {"title": "KeySuggestionDto", "type": "object", "description": "model describing payload used to send resof verifying key for a tenant", "properties": {"available": {"type": "boolean"}, "suggestions": {"type": "array", "items": {"type": "string"}}}, "required": ["available"], "additionalProperties": false}, "VerifyKeyDto": {"title": "VerifyKeyDto", "type": "object", "description": "model describing payload used to verify key for a tenant", "properties": {"key": {"type": "string"}}, "required": ["key"], "additionalProperties": false}, "SubscriptionDTO": {"title": "SubscriptionDTO", "type": "object", "description": "(tsType: SubscriptionDTO, schemaOptions: { title: 'SubscriptionDTO' })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time"}, "deletedBy": {"type": "string"}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "subscriberId": {"type": "string"}, "startDate": {"type": "string"}, "endDate": {"type": "string"}, "status": {"type": "number"}, "planId": {"type": "string"}, "invoiceId": {"type": "string"}, "plan": {"type": "object"}}, "additionalProperties": false, "x-typescript-type": "SubscriptionDTO"}, "UpdateTenantDtoPartial": {"title": "UpdateTenantDtoPartial", "type": "object", "description": "model describing payload used to create and onboard a tenant (tsType: Partial<UpdateTenantDto>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string", "description": "name of the tenant", "minLength": 3, "maxLength": 50, "pattern": "^(?![-\\s])[a-zA-Z0-9\\s&.,'’-]+(?<![-\\s])$", "errorMessage": {"pattern": "Invalid name format. Use only letters, numbers, spaces, &, ., ,, ', and –. It cannot start or end with a space or hyphen."}}, "status": {"type": "number", "description": "status of a tenant, it can be - 0(active), 1(provisioning),2(deprovisioning),3(inactive)", "enum": [0, 1, 2, 3, 4, 5]}, "planId": {"type": "string", "description": "id of the plan associated with this tenant."}, "key": {"type": "string", "description": "A short string used to identify a tenant. This is also used as the namespace and subdomain for this particular tenant.", "pattern": "^(?!-)[a-zA-Z0-9-]{3,63}(?<!-)$", "minLength": 3, "maxLength": 63}, "spocUserId": {"type": "string", "description": "user id of the admin user who acts as a spoc for this tenant."}, "domains": {"type": "array", "items": {"type": "string", "description": "array of domains that are allowed for this tenant"}}, "leadId": {"type": "string", "description": "id of the lead from which this tenant was generated. this is optional as a tenant can be created without this lead."}, "addressId": {"type": "string", "description": "id of the address of the tenant"}, "lang": {"type": "string"}, "planName": {"type": "string"}, "selectedFiles": {"type": "array", "items": {"type": "object"}}, "contact": {"type": "object", "description": "main model of the service that represents a tenant in the system, either pooled or siloed (tsType: Omit<Contact, 'tenantId' | 'id'>, schemaOptions: { exclude: [ 'tenantId', 'id' ] })", "title": "ContactExcluding_tenantId-id_", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "firstName": {"type": "string", "description": "first name of the tenant admin", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "lastName": {"type": "string", "description": "last name of the tenant admin", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "userName": {"type": "string", "description": "user name of the tenant admin", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z0-9]+$"}, "password": {"type": "string", "description": "password of the tenant admin"}, "email": {"type": "string", "description": "email id of the contact", "format": "email", "pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", "errorMessage": {"pattern": "Invalid email format. Please provide a valid email address."}}, "designation": {"type": "string", "nullable": true, "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "phoneNumber": {"type": "string", "nullable": true, "minLength": 10, "maxLength": 10, "pattern": "^\\d{10}$", "errorMessage": {"pattern": "Invalid phone number format. Use only digits.", "minLength": "Phone number must be at least 10 digits long.", "maxLength": "Phone number cannot exceed 10 characters."}}, "countryCode": {"type": "string", "minLength": 2, "maxLength": 5, "pattern": "^\\+[0-9]{1,4}$", "errorMessage": {"pattern": "Invalid country code format. It should start with an + followed by 1 to 4 digits.", "minLength": "Country code must be at least 2 characters.", "maxLength": "Country code cannot exceed 5 characters."}}, "isPrimary": {"type": "boolean", "description": "boolean value denoting if the contact is a primary contact for it's tenant."}, "type": {"type": "string", "description": "type of the contact"}}, "required": ["firstName", "lastName", "userName", "email", "isPrimary"], "additionalProperties": false}, "city": {"type": "string", "description": "city of the tenant admin"}, "state": {"type": "string", "description": "state of the tenant admin"}}, "additionalProperties": false}, "UpdateTenantDto": {"title": "UpdateTenantDto", "type": "object", "description": "model describing payload used to create and onboard a tenant", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string", "description": "name of the tenant", "minLength": 3, "maxLength": 50, "pattern": "^(?![-\\s])[a-zA-Z0-9\\s&.,'’-]+(?<![-\\s])$", "errorMessage": {"pattern": "Invalid name format. Use only letters, numbers, spaces, &, ., ,, ', and –. It cannot start or end with a space or hyphen."}}, "status": {"type": "number", "description": "status of a tenant, it can be - 0(active), 1(provisioning),2(deprovisioning),3(inactive)", "enum": [0, 1, 2, 3, 4, 5]}, "planId": {"type": "string", "description": "id of the plan associated with this tenant."}, "key": {"type": "string", "description": "A short string used to identify a tenant. This is also used as the namespace and subdomain for this particular tenant.", "pattern": "^(?!-)[a-zA-Z0-9-]{3,63}(?<!-)$", "minLength": 3, "maxLength": 63}, "spocUserId": {"type": "string", "description": "user id of the admin user who acts as a spoc for this tenant."}, "domains": {"type": "array", "items": {"type": "string", "description": "array of domains that are allowed for this tenant"}}, "leadId": {"type": "string", "description": "id of the lead from which this tenant was generated. this is optional as a tenant can be created without this lead."}, "addressId": {"type": "string", "description": "id of the address of the tenant"}, "lang": {"type": "string"}, "planName": {"type": "string"}, "selectedFiles": {"type": "array", "items": {"type": "object"}}, "contact": {"type": "object", "description": "main model of the service that represents a tenant in the system, either pooled or siloed (tsType: Omit<Contact, 'tenantId' | 'id'>, schemaOptions: { exclude: [ 'tenantId', 'id' ] })", "title": "ContactExcluding_tenantId-id_", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "firstName": {"type": "string", "description": "first name of the tenant admin", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "lastName": {"type": "string", "description": "last name of the tenant admin", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "userName": {"type": "string", "description": "user name of the tenant admin", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z0-9]+$"}, "password": {"type": "string", "description": "password of the tenant admin"}, "email": {"type": "string", "description": "email id of the contact", "format": "email", "pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", "errorMessage": {"pattern": "Invalid email format. Please provide a valid email address."}}, "designation": {"type": "string", "nullable": true, "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "phoneNumber": {"type": "string", "nullable": true, "minLength": 10, "maxLength": 10, "pattern": "^\\d{10}$", "errorMessage": {"pattern": "Invalid phone number format. Use only digits.", "minLength": "Phone number must be at least 10 digits long.", "maxLength": "Phone number cannot exceed 10 characters."}}, "countryCode": {"type": "string", "minLength": 2, "maxLength": 5, "pattern": "^\\+[0-9]{1,4}$", "errorMessage": {"pattern": "Invalid country code format. It should start with an + followed by 1 to 4 digits.", "minLength": "Country code must be at least 2 characters.", "maxLength": "Country code cannot exceed 5 characters."}}, "isPrimary": {"type": "boolean", "description": "boolean value denoting if the contact is a primary contact for it's tenant."}, "type": {"type": "string", "description": "type of the contact"}}, "required": ["firstName", "lastName", "userName", "email", "isPrimary"], "additionalProperties": false}, "city": {"type": "string", "description": "city of the tenant admin"}, "state": {"type": "string", "description": "state of the tenant admin"}}, "required": ["name", "status", "key", "domains", "selectedFiles"], "additionalProperties": false}, "TenantMgmtConfig": {"title": "TenantMgmtConfig", "type": "object", "description": "tenant_mgmt_configs to save any tenant specific data related to idP", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "configKey": {"type": "string"}, "configValue": {"type": "object"}, "tenantId": {"type": "string", "description": "id of the tenant this invoice is generated for"}}, "required": ["config<PERSON><PERSON>", "config<PERSON><PERSON><PERSON>", "tenantId"], "additionalProperties": false}, "NewTenantConfig": {"title": "NewTenantConfig", "type": "object", "description": "tenant_mgmt_configs to save any tenant specific data related to idP (tsType: Omit<TenantMgmtConfig, 'id'>, schemaOptions: { title: 'NewTenantConfig', exclude: [ 'id' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "configKey": {"type": "string"}, "configValue": {"type": "object"}, "tenantId": {"type": "string", "description": "id of the tenant this invoice is generated for"}}, "required": ["config<PERSON><PERSON>", "config<PERSON><PERSON><PERSON>", "tenantId"], "additionalProperties": false}, "TenantMgmtConfigWithRelations": {"title": "TenantMgmtConfigWithRelations", "type": "object", "description": "tenant_mgmt_configs to save any tenant specific data related to idP (tsType: TenantMgmtConfigWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "configKey": {"type": "string"}, "configValue": {"type": "object"}, "tenantId": {"type": "string", "description": "id of the tenant this invoice is generated for"}, "tenant": {"$ref": "#/components/schemas/TenantWithRelations"}, "foreignKey": {}}, "required": ["config<PERSON><PERSON>", "config<PERSON><PERSON><PERSON>", "tenantId"], "additionalProperties": false}, "TenantMgmtConfigPartial": {"title": "TenantMgmtConfigPartial", "type": "object", "description": "tenant_mgmt_configs to save any tenant specific data related to idP (tsType: Partial<TenantMgmtConfig>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "configKey": {"type": "string"}, "configValue": {"type": "object"}, "tenantId": {"type": "string", "description": "id of the tenant this invoice is generated for"}}, "additionalProperties": false}, "loopback.Count": {"type": "object", "title": "loopback.Count", "x-typescript-type": "@loopback/repository#Count", "properties": {"count": {"type": "number"}}}, "contacts.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "contacts.<PERSON><PERSON><PERSON><PERSON>er"}, "contacts.IncludeFilter.Items": {"title": "contacts.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["tenant"]}, "scope": {"$ref": "#/components/schemas/contacts.ScopeFilter"}}}, "contacts.Filter": {"type": "object", "title": "contacts.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "firstName": {"type": "boolean"}, "lastName": {"type": "boolean"}, "email": {"type": "boolean"}, "isPrimary": {"type": "boolean"}, "type": {"type": "boolean"}, "tenantId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "firstName", "lastName", "email", "isPrimary", "type", "tenantId"], "example": "deleted"}, "uniqueItems": true}], "title": "contacts.Fields"}, "include": {"title": "contacts.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/contacts.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Contact>"}, "contacts.Filter1": {"type": "object", "title": "contacts.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "contacts.Where<PERSON><PERSON>er", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "firstName": {"type": "boolean"}, "lastName": {"type": "boolean"}, "email": {"type": "boolean"}, "isPrimary": {"type": "boolean"}, "type": {"type": "boolean"}, "tenantId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "firstName", "lastName", "email", "isPrimary", "type", "tenantId"], "example": "deleted"}, "uniqueItems": true}], "title": "contacts.Fields"}, "include": {"title": "contacts.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/contacts.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Contact>"}, "invoices.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "invoices.ScopeFilter"}, "invoices.IncludeFilter.Items": {"title": "invoices.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["tenant"]}, "scope": {"$ref": "#/components/schemas/invoices.ScopeFilter"}}}, "invoices.Filter": {"type": "object", "title": "invoices.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "startDate": {"type": "boolean"}, "endDate": {"type": "boolean"}, "amount": {"type": "boolean"}, "currencyCode": {"type": "boolean"}, "invoiceFile": {"type": "boolean"}, "dueDate": {"type": "boolean"}, "status": {"type": "boolean"}, "tenantId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "startDate", "endDate", "amount", "currencyCode", "invoiceFile", "dueDate", "status", "tenantId"], "example": "deleted"}, "uniqueItems": true}], "title": "invoices.Fields"}, "include": {"title": "invoices.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/invoices.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Invoice>"}, "invoices.Filter1": {"type": "object", "title": "invoices.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "invoices.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "startDate": {"type": "boolean"}, "endDate": {"type": "boolean"}, "amount": {"type": "boolean"}, "currencyCode": {"type": "boolean"}, "invoiceFile": {"type": "boolean"}, "dueDate": {"type": "boolean"}, "status": {"type": "boolean"}, "tenantId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "startDate", "endDate", "amount", "currencyCode", "invoiceFile", "dueDate", "status", "tenantId"], "example": "deleted"}, "uniqueItems": true}], "title": "invoices.Fields"}, "include": {"title": "invoices.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/invoices.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Invoice>"}, "leads.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "leads.<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "leads.IncludeFilter.Items": {"title": "leads.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["tenant", "address"]}, "scope": {"$ref": "#/components/schemas/leads.ScopeFilter"}}}, "leads.Filter": {"type": "object", "title": "leads.<PERSON><PERSON>", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "firstName": {"type": "boolean"}, "lastName": {"type": "boolean"}, "companyName": {"type": "boolean"}, "email": {"type": "boolean"}, "designation": {"type": "boolean"}, "phoneNumber": {"type": "boolean"}, "countryCode": {"type": "boolean"}, "status": {"type": "boolean"}, "isValidated": {"type": "boolean"}, "addressId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "firstName", "lastName", "companyName", "email", "designation", "phoneNumber", "countryCode", "status", "isValidated", "addressId"], "example": "deleted"}, "uniqueItems": true}], "title": "leads.<PERSON>"}, "include": {"title": "leads.<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/leads.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Lead>"}, "leads.Filter1": {"type": "object", "title": "leads.<PERSON><PERSON>", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "leads.<PERSON><PERSON><PERSON><PERSON>", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "firstName": {"type": "boolean"}, "lastName": {"type": "boolean"}, "companyName": {"type": "boolean"}, "email": {"type": "boolean"}, "designation": {"type": "boolean"}, "phoneNumber": {"type": "boolean"}, "countryCode": {"type": "boolean"}, "status": {"type": "boolean"}, "isValidated": {"type": "boolean"}, "addressId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "firstName", "lastName", "companyName", "email", "designation", "phoneNumber", "countryCode", "status", "isValidated", "addressId"], "example": "deleted"}, "uniqueItems": true}], "title": "leads.<PERSON>"}, "include": {"title": "leads.<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/leads.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Lead>"}, "PingResponse": {"type": "object", "title": "PingResponse", "properties": {"greeting": {"type": "string"}, "date": {"type": "string"}, "url": {"type": "string"}, "headers": {"type": "object", "properties": {"Content-Type": {"type": "string"}}, "additionalProperties": true}}}, "tenant_mgmt_configs.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "tenant_mgmt_configs.ScopeFilter"}, "tenant_mgmt_configs.IncludeFilter.Items": {"title": "tenant_mgmt_configs.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["tenant"]}, "scope": {"$ref": "#/components/schemas/tenant_mgmt_configs.ScopeFilter"}}}, "tenant_mgmt_configs.Filter": {"type": "object", "title": "tenant_mgmt_configs.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "configKey": {"type": "boolean"}, "configValue": {"type": "boolean"}, "tenantId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "config<PERSON><PERSON>", "config<PERSON><PERSON><PERSON>", "tenantId"], "example": "deleted"}, "uniqueItems": true}], "title": "tenant_mgmt_configs.Fields"}, "include": {"title": "tenant_mgmt_configs.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/tenant_mgmt_configs.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<TenantMgmtConfig>"}, "tenant_mgmt_configs.Filter1": {"type": "object", "title": "tenant_mgmt_configs.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "tenant_mgmt_configs.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "configKey": {"type": "boolean"}, "configValue": {"type": "boolean"}, "tenantId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "config<PERSON><PERSON>", "config<PERSON><PERSON><PERSON>", "tenantId"], "example": "deleted"}, "uniqueItems": true}], "title": "tenant_mgmt_configs.Fields"}, "include": {"title": "tenant_mgmt_configs.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/tenant_mgmt_configs.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<TenantMgmtConfig>"}, "tenants.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "tenants.ScopeFilter"}, "tenants.IncludeFilter.Items": {"title": "tenants.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["contacts", "resources", "lead", "address", "files"]}, "scope": {"$ref": "#/components/schemas/tenants.ScopeFilter"}}}, "tenants.Filter": {"type": "object", "title": "tenants.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "tenants.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "name": {"type": "boolean"}, "status": {"type": "boolean"}, "planId": {"type": "boolean"}, "key": {"type": "boolean"}, "spocUserId": {"type": "boolean"}, "domains": {"type": "boolean"}, "leadId": {"type": "boolean"}, "addressId": {"type": "boolean"}, "lang": {"type": "boolean"}, "planName": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "name", "status", "planId", "key", "spocUserId", "domains", "leadId", "addressId", "lang", "planName"], "example": "deleted"}, "uniqueItems": true}], "title": "tenants.Fields"}, "include": {"title": "tenants.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/tenants.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Tenant>"}}}, "servers": [{"url": "/"}]}