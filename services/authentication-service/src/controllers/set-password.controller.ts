﻿// Copyright (c) 2023 Sourcefuse Technologies
//
// This software is released under the MIT License.
// https://opensource.org/licenses/MIT

import {PermissionKey, UpdatePasswordDto} from '@local/core';
import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors, getModelSchemaRef, post, requestBody} from '@loopback/rest';
import {
  AuthClientRepository,
  UserCredentialsRepository,
  UserTenantRepository,
} from '@sourceloop/authentication-service';
import {
  CONTENT_TYPE,
  ErrorCodes,
  IAuthUserWithPermissions,
  ILogger,
  LOGGER,
  OPERATION_SECURITY_SPEC,
  STATUS_CODE,
  UserStatus,
} from '@sourceloop/core';
import * as bcrypt from 'bcrypt';
import {
  AuthenticationBindings,
  STRATEGY,
  authenticate,
} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';

const basePath = 'auth/set-password';
const saltRounds = 10;

/**
 * Controller to handle set password verification and update operations.
 *
 * Provides an endpoint for authenticated users to set or update their password,
 * verifying client credentials and updating user status as needed.
 */
export class SetPasswordController {
  /**
   * Constructs the SetPasswordController with required dependencies.
   * @param userCredentialsRepository - Repository for user credentials.
   * @param authClientRepo - Repository for authentication clients.
   * @param userTenantRepository - Repository for user-tenant associations.
   * @param logger - Logger instance for logging.
   * @param currentUser - The currently authenticated user.
   */
  constructor(
    @repository(UserCredentialsRepository)
    private readonly userCredentialsRepository: UserCredentialsRepository,

    @repository(AuthClientRepository)
    private readonly authClientRepo: AuthClientRepository,

    @repository(UserTenantRepository)
    private readonly userTenantRepository: UserTenantRepository,

    @inject(LOGGER.LOGGER_INJECT)
    private readonly logger: ILogger,

    @inject(AuthenticationBindings.CURRENT_USER)
    private readonly currentUser: IAuthUserWithPermissions,
  ) {}

  /**
   * Verifies the forget-password token and updates the user's password.
   *
   * This endpoint requires a valid client ID and secret, and updates the password
   * for the currently authenticated user. If user credentials do not exist, they are created.
   * The user's tenant status is also set to ACTIVE if a user-tenant record exists.
   *
   * @param dto - DTO containing the new password and client credentials.
   * @throws {HttpErrors.BadRequest} If clientId or clientSecret is missing.
   * @throws {HttpErrors.Unauthorized} If provided client credentials are invalid.
   * @throws {HttpErrors.NotFound} If user credentials are not found.
   * @returns {Promise<void>} A successful password update returns no content.
   */
  @authorize({
    permissions: [PermissionKey.UpdatePassword],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @post(`${basePath}/verify`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Success Response.',
      },
      ...ErrorCodes,
    },
  })
  async updatePassword(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(UpdatePasswordDto),
        },
      },
    })
    dto: UpdatePasswordDto,
  ): Promise<void> {
    // Ensure both clientId and clientSecret are provided
    if (!dto.clientId && !dto.clientSecret) {
      throw new HttpErrors.BadRequest(
        'Client ID and Client Secret are required',
      );
    }

    // Validate the client credentials
    const authClient = await this.authClientRepo.findOne({
      where: {
        clientId: dto.clientId,
        clientSecret: dto.clientSecret,
      },
    });

    if (!authClient) {
      this.logger.error(`Auth client with ID ${dto.clientId} does not exist`);
      throw new HttpErrors.Unauthorized('Invalid client credentials');
    }

    // Find user credentials for the current user
    const credentials = await this.userCredentialsRepository.findOne({
      where: {userId: this.currentUser.id},
    });

    // Hash the new password
    const hashedPassword = await bcrypt.hash(dto.newPassword, saltRounds);

    // Create or update user credentials
    if (!credentials) {
      await this.userCredentialsRepository.create({
        userId: this.currentUser.id,
        password: hashedPassword,
      });
    } else {
      await this.userCredentialsRepository.updateById(credentials.id, {
        password: hashedPassword,
      });
    }

    // Update user-tenant status to ACTIVE if record exists
    const userTenant = await this.userTenantRepository.findOne({
      where: {userId: this.currentUser.id},
    });

    if (userTenant) {
      await this.userTenantRepository.updateById(userTenant.id, {
        status: UserStatus.ACTIVE,
      });
    }
  }
}
