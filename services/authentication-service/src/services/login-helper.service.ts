import {BindingScope, injectable} from '@loopback/context';
import {HttpErrors} from '@loopback/rest';
import {
  AuthClient,
  AuthUser,
  IAuthClientDTO,
  LoginHelperService,
  UserTenant,
} from '@sourceloop/authentication-service';
import {UserStatus} from '@sourceloop/core';

@injectable({
  scope: BindingScope.TRANSIENT,
  tags: {type: 'service', serviceName: 'LoginHelperService'},
})
export class LoginHelperService1 extends LoginHelperService {
  async verifyClientUserLogin(
    req: IAuthClientDTO,
    client?: AuthClient,
    reqUser?: Pick<AuthUser, 'id' | 'authClientIds'> | null,
  ): Promise<Pick<UserTenant, 'status'> | null> {
    const userStatus = await super.verifyClientUserLogin(req, client, reqUser);
    if (userStatus?.status === UserStatus.INACTIVE) {
      throw new HttpErrors.BadRequest('User not active yet');
    } else {
      // Do nothing and move ahead
    }
    return userStatus;
  }
}
