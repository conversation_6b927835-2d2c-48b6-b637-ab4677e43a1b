import {Per<PERSON><PERSON><PERSON>} from '@local/core';
import {BindingScope} from '@loopback/context';
import {Client} from '@loopback/testlab';
import {
  AuthClient,
  AuthClientRepository,
  UserCredentials,
  UserCredentialsRepository,
  UserTenant,
  UserTenantRepository,
} from '@sourceloop/authentication-service';
import {ILogger, LOGGER, STATUS_CODE, UserStatus} from '@sourceloop/core';
import sinon from 'sinon';
import {AuthenticationServiceApplication} from '../../application';
import {
  mockAuthClient,
  mockCredentials,
  mockUpdatePasswordDto,
  mockUserTenant,
} from './mock-data';
import {getToken, setupApplication} from './test-helper';

describe('setPasswordController', () => {
  let app: AuthenticationServiceApplication;
  let client: Client;
  let userCredentialsRepositoryStub: sinon.SinonStubbedInstance<UserCredentialsRepository>;
  let authClientRepoStub: sinon.SinonStubbedInstance<AuthClientRepository>;
  let userTenantRepoStub: sinon.SinonStubbedInstance<UserTenantRepository>;

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
    const logger = app.getSync<ILogger>(LOGGER.LOGGER_INJECT);
    app.bind(LOGGER.LOGGER_INJECT).to(logger).inScope(BindingScope.SINGLETON);
  });

  after(async () => {
    await app.stop();
  });

  beforeEach(async () => {
    userCredentialsRepositoryStub = {
      findOne: sinon
        .stub<
          [
            filter?: import('@loopback/repository').Filter<UserCredentials>,
            options?: import('@loopback/repository').Options,
          ],
          Promise<UserCredentials>
        >()
        .resolves(mockCredentials),
      updateById: sinon
        .stub<
          [
            id: string,
            data: Partial<UserCredentials>,
            options?: import('@loopback/repository').Options,
          ],
          Promise<void>
        >()
        .resolves(),
      create: sinon
        .stub<
          [
            data: Partial<UserCredentials>,
            options?: import('@loopback/repository').Options,
          ],
          Promise<UserCredentials>
        >()
        .resolves(mockCredentials),
    } as unknown as sinon.SinonStubbedInstance<UserCredentialsRepository>;

    authClientRepoStub = {
      findOne: sinon
        .stub<
          [
            filter?: import('@loopback/repository').Filter<AuthClient>,
            options?: import('@loopback/repository').Options,
          ],
          Promise<AuthClient>
        >()
        .resolves(mockAuthClient),
    } as unknown as sinon.SinonStubbedInstance<AuthClientRepository>;

    userTenantRepoStub = {
      findOne: sinon
        .stub<
          [
            filter?: import('@loopback/repository').Filter<UserTenant>,
            options?: import('@loopback/repository').Options,
          ],
          Promise<UserTenant>
        >()
        .resolves(mockUserTenant),
      updateById: sinon
        .stub<
          [
            id: string,
            data: Partial<UserTenant>,
            options?: import('@loopback/repository').Options,
          ],
          Promise<void>
        >()
        .resolves(),
    } as unknown as sinon.SinonStubbedInstance<UserTenantRepository>;

    app
      .bind('repositories.UserCredentialsRepository')
      .to(userCredentialsRepositoryStub);
    app.bind('repositories.AuthClientRepository').to(authClientRepoStub);
    app.bind('repositories.UserTenantRepository').to(userTenantRepoStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  it('invokes POST /auth/set-password/verify with valid token and data', async () => {
    const token = getToken([PermissionKey.UpdatePassword]);
    const dto = {...mockUpdatePasswordDto};
    await client
      .post('/auth/set-password/verify')
      .set('Authorization', token)
      .send(dto)
      .expect(STATUS_CODE.NO_CONTENT);
  });

  it('returns 401 if client credentials are invalid', async () => {
    authClientRepoStub.findOne.resolves(null);
    const token = getToken([PermissionKey.UpdatePassword]);
    const dto = {...mockUpdatePasswordDto};
    await client
      .post('/auth/set-password/verify')
      .set('Authorization', token)
      .send(dto)
      .expect(401);
  });

  it('creates credentials if not found', async () => {
    userCredentialsRepositoryStub.findOne.resolves(null);
    const token = getToken([PermissionKey.UpdatePassword]);
    const dto = {...mockUpdatePasswordDto};
    await client
      .post('/auth/set-password/verify')
      .set('Authorization', token)
      .send(dto)
      .expect(STATUS_CODE.NO_CONTENT);
  });

  it('updates userTenant status to ACTIVE if userTenant exists', async () => {
    const updateByIdSpy = userTenantRepoStub.updateById;
    const token = getToken([PermissionKey.UpdatePassword]);
    const dto = {...mockUpdatePasswordDto};
    await client
      .post('/auth/set-password/verify')
      .set('Authorization', token)
      .send(dto)
      .expect(STATUS_CODE.NO_CONTENT);
    sinon.assert.calledWith(
      updateByIdSpy,
      mockUserTenant.id,
      sinon.match.has('status', UserStatus.ACTIVE),
    );
  });
});
