import {injectable, BindingScope, inject, service} from '@loopback/core';
import Strip<PERSON> from 'stripe';
import {IBillingService} from '../types';
import {repository} from '@loopback/repository';
import {BillingComponentBindings} from 'loopback4-billing';
import {HttpErrors} from '@loopback/rest';
import {
  getEndDate,
  InvoiceResponse,
  InvoiceStatus,
  StripeEventPayload,
  StripeEventResponse,
  StripeEvents,
} from '@local/core';
import moment from 'moment';
import {DEFAULT_INVOICE_DUE_DAYS} from '../constants';
import {SubscriptionStatus} from '@sourceloop/ctrl-plane-subscription-service';
import {BillingCustomer, Invoice} from '../models';
import {UnitHelperService} from './unit-helper.service';
import {
  InvoiceRepository,
  BillingCustomerRepository,
  SubscriptionRepository,
} from '../repositories';

/**
 * Service responsible for handling Stripe webhook events.
 *
 * It processes subscription updates, invoice creations,
 * and invoice payments, and updates the local billing system accordingly.
 */
@injectable({scope: BindingScope.TRANSIENT})
export class StripeWebhookService {
  /**
   * Number of days before an invoice is due (defaults to env `INVOICE_DUE_DAYS` or {@link DEFAULT_INVOICE_DUE_DAYS}).
   */
  invoiceDueDays: number;

  /**
   * Creates a new instance of {@link StripeWebhookService}.
   *
   * @param billingProvider - Provider for billing service (Stripe SDK wrapper).
   * @param subscriptionRepository - Repository for managing subscriptions.
   * @param billingCustomerRepository - Repository for managing billing customers.
   * @param invoiceRepository - Repository for managing invoices.
   * @param unitHelperService - Helper service for currency unit conversions.
   */
  constructor(
    @inject(BillingComponentBindings.SDKProvider)
    private readonly billingProvider: IBillingService,
    @repository(SubscriptionRepository)
    private readonly subscriptionRepository: SubscriptionRepository,
    @repository(BillingCustomerRepository)
    private readonly billingCustomerRepository: BillingCustomerRepository<BillingCustomer>,
    @repository(InvoiceRepository)
    private readonly invoiceRepository: InvoiceRepository<Invoice>,
    @service(UnitHelperService)
    private readonly unitHelperService: UnitHelperService,
  ) {
    this.invoiceDueDays = process.env.INVOICE_DUE_DAYS
      ? Number(process.env.INVOICE_DUE_DAYS)
      : DEFAULT_INVOICE_DUE_DAYS;
  }

  /**
   * Handles incoming Stripe webhook events and dispatches them to the appropriate handler.
   *
   * @param event - The Stripe webhook event payload.
   * @returns A response containing the event result.
   * @throws {@link HttpErrors.BadRequest} If the event type is unsupported.
   */
  async handleWebhookEvent(
    event: StripeEventPayload,
  ): Promise<StripeEventResponse<InvoiceResponse | undefined>> {
    switch (event.type) {
      case StripeEvents.INVOICE_CREATED:
        return this.handleInvoiceCreatedEvent(event.data);
      case StripeEvents.INVOICE_PAID:
        return this.handleInvoicePaidEvent(event.data);
      case StripeEvents.SUBSCRIPTION_UPDATE:
        return this.handleSubscriptionUpdateEvent(event.data);
      case StripeEvents.INVOICE_UPDATED:
      case StripeEvents.INVOICE_SENT:
      default:
        throw new HttpErrors.BadRequest('Invalid event type');
    }
  }

  /**
   * Handles subscription update events from Stripe.
   *
   * Updates the subscription status, start, and end date in the local system.
   *
   * @param eventData - Stripe subscription update event data.
   * @returns A response indicating success and whether provisioning is required.
   */
  private async handleSubscriptionUpdateEvent(
    eventData: Stripe.CustomerSubscriptionUpdatedEvent.Data,
  ): Promise<StripeEventResponse<undefined>> {
    console.info('Event Data object: ', eventData.object);
    console.info('previous items', eventData.previous_attributes);

    const subscription = eventData.object;
    const previousAttributes = eventData.previous_attributes;
    if (subscription.status === 'active') {
      const startAndEndDate = getEndDate(
        subscription.billing_cycle_anchor,
        subscription.items.data[0].plan.interval,
        subscription.items.data[0].plan.interval_count,
      );
      await this.subscriptionRepository.updateAll(
        {
          status: SubscriptionStatus.ACTIVE,
          startDate: startAndEndDate.startDate,
          endDate: startAndEndDate.endDate,
        },
        {externalSubscriptionId: subscription.id},
      );
      const subscriptionDto = await this.subscriptionRepository.findOne({
        where: {externalSubscriptionId: subscription.id},
        include: [{relation: 'plan'}],
      });
      if (previousAttributes?.status === 'incomplete') {
        return {
          event: StripeEvents.SUBSCRIPTION_UPDATE,
          success: true,
          isProvisionRequired: true,
          message: JSON.stringify(subscriptionDto),
        };
      }
      return {
        event: StripeEvents.SUBSCRIPTION_UPDATE,
        success: true,
      };
    }
    return {event: StripeEvents.SUBSCRIPTION_UPDATE, success: true};
  }

  /**
   * Handles invoice payment events from Stripe.
   *
   * Updates the invoice status to `PAID` in the local system.
   *
   * @param eventData - Stripe invoice paid event data.
   * @returns A response indicating success.
   */
  private async handleInvoicePaidEvent(
    eventData: Stripe.InvoicePaidEvent.Data,
  ): Promise<StripeEventResponse<undefined>> {
    const data = eventData.object;
    await this.invoiceRepository.updateAll(
      {
        invoiceStatus: InvoiceStatus.PAID,
      },
      {
        invoiceId: data.id,
      },
    );
    return {event: StripeEvents.INVOICE_PAID, success: true};
  }

  /**
   * Handles invoice creation events from Stripe.
   *
   * Creates a new invoice record in the local system and, in case of incomplete subscriptions,
   * prepares invoice information for email notifications.
   *
   * @param eventData - Stripe invoice created event data.
   * @returns A response indicating success and optional invoice information.
   */
  private async handleInvoiceCreatedEvent(
    eventData: Stripe.InvoiceCreatedEvent.Data,
  ): Promise<StripeEventResponse<undefined>> {
    const data = eventData.object;
    const lineItem = data?.lines?.data[0];
    const billingCustomer = await this.billingCustomerRepository.findOne({
      where: {customerId: data.customer as string},
    });
    if (!billingCustomer) {
      return {event: StripeEvents.INVOICE_CREATED, success: false};
    }

    const newInvoice = new Invoice({
      invoiceId: data.id,
      invoiceStatus: data.status as InvoiceStatus, // NOSONAR
      billingCustomerId: billingCustomer.id,
      amount: parseFloat((data.amount_due / 100).toFixed(2)), // Stripe amounts are in cents
      tax: this.calculateTaxAndDiscountUtil(
        data.total_tax_amounts,
        data.currency,
      ),
      discount: this.calculateTaxAndDiscountUtil(
        data.total_discount_amounts,
        data.currency,
      ),
      dueDate: moment
        .unix(data.due_date ?? moment().add(this.invoiceDueDays, 'days').unix())
        .toISOString(),
      startDate: moment.unix(lineItem?.period.start).toISOString(),
      endDate: moment.unix(lineItem?.period.end).toISOString(),
    });
    await this.invoiceRepository.create(newInvoice);
    const subscriptionId = data.parent?.subscription_details?.subscription;

    const subscription = await this.billingProvider.getSubscription(
      subscriptionId as string,
    );
    let invoiceInfo = {};
    if (subscription.status === 'incomplete') {
      invoiceInfo = {
        sendEmail: true,
        message: data.hosted_invoice_url,
        email: data.customer_email,
        userId: billingCustomer.tenantId,
      };
    }
    return {event: StripeEvents.INVOICE_CREATED, success: true, ...invoiceInfo};
  }

  /**
   * Utility function to calculate tax or discount amounts in major currency units.
   *
   * @param taxDiscountArr - List of tax or discount amounts from Stripe.
   * @param currency - The currency code (e.g., `usd`, `eur`).
   * @returns The total amount converted to major units.
   */
  private calculateTaxAndDiscountUtil(
    taxDiscountArr:
      | Stripe.Invoice.TotalDiscountAmount[]
      | Stripe.Invoice.TotalTaxAmount[]
      | null,
    currency: string,
  ): number {
    return (
      taxDiscountArr?.reduce(
        (sum, tax) =>
          sum + this.unitHelperService.convertToMajorUnit(tax.amount, currency),
        0,
      ) ?? 0
    );
  }
}
