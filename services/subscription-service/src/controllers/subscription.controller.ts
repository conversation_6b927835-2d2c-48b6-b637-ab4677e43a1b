import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  HttpErrors,
} from '@loopback/rest';
import moment, {unitOfTime} from 'moment';
import {authorize} from 'loopback4-authorization';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {OPERATION_SECURITY_SPEC, STATUS_CODE} from '@sourceloop/core';
import {
  PlanRepository,
  SubscriptionRepository,
  BillingCustomerRepository,
} from '../repositories';
import {PermissionKey} from '@local/core';
import {BillingCustomer, Subscription, SubscriptionDto} from '../models';
import {
  BillingCycleRepository,
  SubscriptionStatus,
} from '@sourceloop/ctrl-plane-subscription-service';
import {BillingComponentBindings} from 'loopback4-billing';
import {CollectionMethod, IBillingService} from '../types';
import {inject} from '@loopback/core';

const basePath = '/subscriptions';
const DATE_FORMAT = 'YYYY-MM-DD';
const description = 'Array of Subscription model instances';

/**
 * Controller for managing subscription entities.
 * Provides endpoints to create, read, update, delete,
 * and perform status-based queries on subscriptions.
 */
export class SubscriptionController {
  /**
   * Creates an instance of SubscriptionController.
   *
   * @param subscriptionRepository - Repository for Subscription model operations.
   * @param planRepository - Repository for Plan model operations.
   * @param billingCycleRepository - Repository for BillingCycle model operations.
   */
  constructor(
    @repository(SubscriptionRepository)
    public subscriptionRepository: SubscriptionRepository<Subscription>,
    @repository(PlanRepository)
    public planRepository: PlanRepository,
    @repository(BillingCycleRepository)
    public billingCycleRepository: BillingCycleRepository,
    @repository(BillingCustomerRepository)
    public billingCustomerRepository: BillingCustomerRepository<BillingCustomer>,
    @inject(BillingComponentBindings.SDKProvider)
    private readonly billingProvider: IBillingService,
  ) {}

  /**
   * Creates a new subscription.
   *
   * @param subscription - Subscription data without id, startDate, and endDate.
   * @returns The created subscription instance.
   */
  @authorize({
    permissions: [PermissionKey.CreateSubscription],
  })
  @authenticate(STRATEGY.BEARER, {passReqToCallback: true})
  @post(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Subscription model instance',
        content: {
          'application/json': {schema: getModelSchemaRef(Subscription)},
        },
      },
    },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Subscription, {
            title: 'NewSubscription',
            exclude: ['id', 'startDate', 'endDate', 'externalSubscriptionId'],
          }),
        },
      },
    })
    subscription: Omit<
      Subscription,
      'id' | 'startDate' | 'endDate' | 'externalSubscriptionId'
    >,
  ): Promise<Subscription> {
    const plan = await this.planRepository.findById(subscription.planId);
    if (!plan) {
      throw new HttpErrors.NotFound(
        `Plan with id ${subscription.planId} not found`,
      );
    }
    const billingCustomer = await this.billingCustomerRepository.find({
      where: {tenantId: subscription.subscriberId},
    });
    const externalSubscriptionId =
      await this.billingProvider.createSubscription({
        customerId: billingCustomer[0]?.customerId ?? '',
        priceRefId: subscription.priceRefId,
        collectionMethod: CollectionMethod.CHARGE_AUTOMATICALLY,
      });

    return this.subscriptionRepository.create({
      ...subscription,
      externalSubscriptionId,
      priceRefId: subscription.priceRefId,
    });
  }

  /**
   * Maps billing cycle duration unit to moment.js duration constructor unit.
   *
   * @param durationUnit - Duration unit (month, year, week, etc.).
   * @returns Moment.js duration unit type.
   */
  private _unitMap(durationUnit: string): unitOfTime.DurationConstructor {
    switch (durationUnit) {
      case 'month':
        return 'M';
      case 'year':
        return 'y';
      case 'week':
        return 'week';
      default:
        return 'days';
    }
  }

  /**
   * Returns the count of subscriptions matching the provided filter.
   *
   * @param where - Optional where filter for count query.
   * @returns Count of subscriptions.
   */
  @authorize({permissions: [PermissionKey.ViewSubscription]})
  @authenticate(STRATEGY.BEARER, {passReqToCallback: true})
  @get(`${basePath}/count`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Subscription model count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async count(
    @param.where(Subscription) where?: Where<Subscription>,
  ): Promise<Count> {
    return this.subscriptionRepository.count(where);
  }

  /**
   * Finds subscriptions with optional filter and relations.
   *
   * @param filter - Optional filter for the query.
   * @returns Array of subscription instances.
   */
  @authorize({permissions: [PermissionKey.ViewSubscription]})
  @authenticate(STRATEGY.BEARER, {passReqToCallback: true})
  @get(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description,
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(Subscription, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(Subscription) filter?: Filter<Subscription>,
  ): Promise<Subscription[]> {
    return this.subscriptionRepository.find(filter);
  }

  /**
   * Retrieves active subscriptions that will expire within 7 days.
   *
   * @returns Array of objects containing subscription ID, days remaining, and subscriber ID.
   */
  @authorize({permissions: [PermissionKey.ViewSubscription]})
  @authenticate(STRATEGY.BEARER, {passReqToCallback: true})
  @get(`${basePath}/expire-soon`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description,
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(Subscription, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async expireSoonSubscription(): Promise<
    {id: string; daysRemainingToExpiry: number; subscriberId: string}[]
  > {
    const daysRemaining = 7;
    const subscriptions = await this.subscriptionRepository.find({
      where: {status: SubscriptionStatus.ACTIVE},
    });

    const expiringSoonSubscriptionObj = [];
    for (const subscription of subscriptions) {
      if (
        moment(subscription.endDate).isBefore(
          moment().add(daysRemaining, 'days'),
        ) &&
        moment(subscription.endDate).isAfter(moment())
      ) {
        const daysRemainingToExpiry = moment(subscription.endDate).diff(
          moment(),
          'days',
        );
        expiringSoonSubscriptionObj.push({
          id: subscription.id,
          daysRemainingToExpiry,
          subscriberId: subscription.subscriberId,
        });
      }
    }
    return expiringSoonSubscriptionObj;
  }

  /**
   * Marks expired subscriptions as EXPIRED and retrieves those expired within the given day range.
   *
   * @param dayCount - Number of days before today to filter expired subscriptions.
   * @returns Array of expired subscriptions within the given date range.
   */
  @authorize({permissions: [PermissionKey.ViewSubscription]})
  @authenticate(STRATEGY.BEARER, {passReqToCallback: true})
  @get(`${basePath}/expired`, {
    description: 'API that will return newly expired subscriptions',
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description,
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(Subscription, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async expiredSubscription(
    @param.header.number('days') dayCount: number,
  ): Promise<{subscriptionId: string; subscriberId: string}[]> {
    const subscriptions = await this.subscriptionRepository.find({
      where: {status: SubscriptionStatus.ACTIVE},
    });

    const markSubscriptionsAsExpiredPromises = [];
    for (const subscription of subscriptions) {
      if (moment(subscription.endDate).isBefore(moment())) {
        markSubscriptionsAsExpiredPromises.push(
          this.subscriptionRepository.updateById(subscription.id, {
            status: SubscriptionStatus.EXPIRED,
          }),
        );
      }
    }
    await Promise.all(markSubscriptionsAsExpiredPromises);

    const range = moment().subtract(dayCount, 'days').format(DATE_FORMAT);
    const expiredSubscriptionWithInRange = [];
    const expiredSubscription = await this.subscriptionRepository.find({
      where: {status: SubscriptionStatus.EXPIRED},
    });

    for (const subscription of expiredSubscription) {
      if (moment(subscription.endDate).isAfter(range)) {
        expiredSubscriptionWithInRange.push({
          subscriptionId: subscription.id,
          subscriberId: subscription.subscriberId,
        });
      }
    }
    return expiredSubscriptionWithInRange;
  }

  /**
   * Updates multiple subscriptions matching the where filter.
   *
   * @param subscription - Partial subscription data to update.
   * @param where - Optional where filter.
   * @returns Count of updated records.
   */
  @authorize({permissions: [PermissionKey.UpdateSubscription]})
  @authenticate(STRATEGY.BEARER, {passReqToCallback: true})
  @patch(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Subscription PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Subscription, {partial: true}),
        },
      },
    })
    subscription: Subscription,
    @param.where(Subscription) where?: Where<Subscription>,
  ): Promise<Count> {
    return this.subscriptionRepository.updateAll(subscription, where);
  }

  /**
   * Finds a subscription by ID.
   *
   * @param id - Subscription ID.
   * @param filter - Optional filter excluding where.
   * @returns The subscription instance.
   */
  @authorize({permissions: [PermissionKey.ViewSubscription]})
  @authenticate(STRATEGY.BEARER, {passReqToCallback: true})
  @get(`${basePath}/{id}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Subscription model instance',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Subscription, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Subscription, {exclude: 'where'})
    filter?: FilterExcludingWhere<Subscription>,
  ): Promise<Subscription> {
    return this.subscriptionRepository.findById(id, filter);
  }

  /**
   * Updates a subscription by ID.
   *
   * @param id - Subscription ID.
   * @param subscription - Partial subscription data to update.
   */
  @authorize({permissions: [PermissionKey.UpdateSubscription]})
  @authenticate(STRATEGY.BEARER, {passReqToCallback: true})
  @patch(`${basePath}/{id}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {description: 'Subscription PATCH success'},
    },
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubscriptionDto, {partial: true}),
        },
      },
    })
    subscriptionDto: SubscriptionDto,
  ): Promise<void> {
    const subscription = await this.subscriptionRepository.findById(id);
    if (!subscription) {
      throw new HttpErrors.NotFound(`Subscription with id ${id} not found`);
    }
    const {prorationBehavior, ...rest} = subscriptionDto;
    await this.billingProvider.updateSubscription(
      subscription.externalSubscriptionId,
      {
        priceRefId: subscriptionDto.priceRefId,
        prorationBehavior: prorationBehavior,
      },
    );
    await this.subscriptionRepository.updateById(id, rest);
  }

  /**
   * Replaces a subscription by ID.
   *
   * @param id - Subscription ID.
   * @param subscription - Full subscription data to replace existing record.
   */
  @authorize({permissions: [PermissionKey.UpdateSubscription]})
  @authenticate(STRATEGY.BEARER, {passReqToCallback: true})
  @put(`${basePath}/{id}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {description: 'Subscription PUT success'},
    },
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() subscription: Subscription,
  ): Promise<void> {
    await this.subscriptionRepository.replaceById(id, subscription);
  }

  /**
   * Deletes a subscription by ID.
   *
   * @param id - Subscription ID.
   */
  @authorize({permissions: [PermissionKey.DeleteSubscription]})
  @authenticate(STRATEGY.BEARER, {passReqToCallback: true})
  @del(`${basePath}/{id}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {description: 'Subscription DELETE success'},
    },
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.subscriptionRepository.deleteById(id);
  }
}
