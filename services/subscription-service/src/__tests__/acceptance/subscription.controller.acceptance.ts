import {Client, expect} from '@loopback/testlab';
import {SubscriptionServiceApplication} from '../..';
import {Subscription} from '../../models';
import {
  BillingCustomerRepository,
  PlanRepository,
  SubscriptionRepository,
} from '../../repositories';
import {
  BillingCycleRepository,
  SubscriptionStatus,
} from '@sourceloop/ctrl-plane-subscription-service';
import {getRepo, getToken, setupApplication} from './test-helper';
import {STATUS_CODE} from '@sourceloop/core';
import {PermissionKey} from '@local/core';
import {mockPlan, mockSubscription} from './mock-data';
import moment from 'moment';
import {IBillingService} from '../../types';
import {StripeService} from '../../services';
import sinon from 'sinon';
import {BillingComponentBindings} from 'loopback4-billing';

describe('SubscriptionController (acceptance)', () => {
  let app: SubscriptionServiceApplication;
  let client: Client;
  let subscriptionRepo: SubscriptionRepository<Subscription>;
  let planRepo: PlanRepository;
  let billingCycleRepo: BillingCycleRepository;
  let billingCustomerRepository: BillingCustomerRepository;
  let savedSubscription: Subscription;
  let billingProvider: sinon.SinonStubbedInstance<IBillingService>;

  const basePath = '/subscriptions';

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
    subscriptionRepo = await getRepo(
      app,
      'repositories.SubscriptionRepository',
    );
    planRepo = await getRepo(app, 'repositories.PlanRepository');
    billingCycleRepo = await getRepo(
      app,
      'repositories.BillingCycleRepository',
    );
    billingCustomerRepository = await getRepo(
      app,
      'repositories.BillingCustomerRepository',
    );
    billingProvider = sinon.createStubInstance<IBillingService>(StripeService);

    billingProvider.createSubscription.resolves('mock-subscription-id');
    app.bind(BillingComponentBindings.SDKProvider).to(billingProvider);
  });

  after(async () => {
    await app.stop();
  });

  beforeEach(async () => {
    await seedData();
  });

  afterEach(async () => {
    await subscriptionRepo.deleteAllHard();
    await planRepo.deleteAllHard();
    sinon.restore();
    await billingCycleRepo.deleteAllHard();
  });

  it('POST /subscriptions creates subscription with calculated dates', async () => {
    const token = getToken([PermissionKey.CreateSubscription]);
    const {id: planId} = await planRepo.create(mockPlan);
    const bc = await billingCycleRepo.create({
      duration: 1,
      durationUnit: 'month',
      cycleName: 'Monthly',
    });
    await planRepo.updateById(planId, {billingCycleId: bc.id});

    const res = await client
      .post(basePath)
      .set('Authorization', token)
      .send({
        subscriberId: 'new-subscriber',
        planId,
        status: SubscriptionStatus.ACTIVE,
        totalCost: 100,
        priceRefId: 'price-123',
      })
      .expect(STATUS_CODE.OK);

    expect(res.body.subscriberId).to.eql('new-subscriber');
  });

  it('PATCH /subscriptions/{id} update subscription', async () => {
    const token = getToken([PermissionKey.UpdateSubscription]);
    const subscription = await subscriptionRepo.create({
      ...mockSubscription,
      totalCost: 50,
    });
    sinon.stub(billingProvider, 'updateSubscription').resolves();

    await client
      .patch(`${basePath}/${subscription.id}`)
      .set('Authorization', token)
      .send({
        totalCost: 100,
      })
      .expect(STATUS_CODE.NO_CONTENT);
  });
  it('GET /subscriptions/count returns count', async () => {
    const token = getToken([PermissionKey.ViewSubscription]);
    const {body} = await client
      .get(`${basePath}/count`)
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);
    expect(body.count).to.eql(1);
  });

  it('GET /subscriptions returns all subscriptions', async () => {
    const token = getToken([PermissionKey.ViewSubscription]);
    const {body} = await client
      .get(basePath)
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);
    expect(body).to.have.length(1);
    expect(body[0].subscriberId).to.eql(mockSubscription.subscriberId);
  });

  it('GET /subscriptions/expire-soon returns expiring subscriptions', async () => {
    const token = getToken([PermissionKey.ViewSubscription]);

    // Set endDate to 3 days from now
    await subscriptionRepo.updateById(savedSubscription.id, {
      endDate: moment().add(3, 'days').format('YYYY-MM-DD'),
    });

    const {body} = await client
      .get(`${basePath}/expire-soon`)
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);

    expect(body).to.have.length(1);
    expect(body[0].daysRemainingToExpiry).to.be.lessThanOrEqual(7);
  });

  it('GET /subscriptions/expired marks expired and returns those within range', async () => {
    const token = getToken([PermissionKey.ViewSubscription]);

    // Expired yesterday
    await subscriptionRepo.updateById(savedSubscription.id, {
      endDate: moment().subtract(1, 'day').format('YYYY-MM-DD'),
    });

    const {body} = await client
      .get(`${basePath}/expired`)
      .set('Authorization', token)
      .set('days', '5')
      .expect(STATUS_CODE.OK);

    expect(body).to.have.length(1);
    expect(body[0].subscriptionId).to.eql(savedSubscription.id);
  });

  it('PATCH /subscriptions updates multiple', async () => {
    const token = getToken([PermissionKey.UpdateSubscription]);
    const {body} = await client
      .patch(basePath)
      .set('Authorization', token)
      .send({subscriberId: 'bulk-updated'})
      .expect(STATUS_CODE.OK);
    expect(body.count).to.eql(1);

    const updated = await subscriptionRepo.findById(savedSubscription.id);
    expect(updated.subscriberId).to.eql('bulk-updated');
  });

  it('GET /subscriptions/{id} returns subscription', async () => {
    const token = getToken([PermissionKey.ViewSubscription]);
    const {body} = await client
      .get(`${basePath}/${savedSubscription.id}`)
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);
    expect(body.id).to.eql(savedSubscription.id);
  });

  it('PATCH /subscriptions/{id} updates single subscription', async () => {
    const token = getToken([PermissionKey.UpdateSubscription]);
    await client
      .patch(`${basePath}/${savedSubscription.id}`)
      .set('Authorization', token)
      .send({subscriberId: 'patched-one'})
      .expect(STATUS_CODE.NO_CONTENT);

    const updated = await subscriptionRepo.findById(savedSubscription.id);
    expect(updated.subscriberId).to.eql('patched-one');
  });

  it('PUT /subscriptions/{id} replaces subscription', async () => {
    const token = getToken([PermissionKey.UpdateSubscription]);
    const replacement = {...savedSubscription, subscriberId: 'replaced-one'};
    await client
      .put(`${basePath}/${savedSubscription.id}`)
      .set('Authorization', token)
      .send(replacement)
      .expect(STATUS_CODE.NO_CONTENT);

    const updated = await subscriptionRepo.findById(savedSubscription.id);
    expect(updated.subscriberId).to.eql('replaced-one');
  });

  it('DELETE /subscriptions/{id} deletes subscription', async () => {
    const token = getToken([PermissionKey.DeleteSubscription]);
    await client
      .delete(`${basePath}/${savedSubscription.id}`)
      .set('Authorization', token)
      .expect(STATUS_CODE.NO_CONTENT);

    const found = await subscriptionRepo
      .findById(savedSubscription.id)
      .catch(err => err);
    expect(found.status).to.eql(STATUS_CODE.NOT_FOUND);
  });

  it('forbids GET /subscriptions without proper permission', async () => {
    const token = getToken([PermissionKey.ViewPlan]);
    await client
      .get(basePath)
      .set('Authorization', token)
      .expect(STATUS_CODE.FORBIDDEN);
  });

  async function seedData() {
    const bc = await billingCycleRepo.create({
      duration: 1,
      durationUnit: 'month',
      cycleName: 'Monthly',
    });
    const plan = await planRepo.create({
      ...mockPlan,
      billingCycleId: bc.id,
    });
    savedSubscription = await subscriptionRepo.create({
      ...mockSubscription,
      planId: plan.id,
      startDate: moment().format('YYYY-MM-DD'),
      endDate: moment().add(10, 'days').format('YYYY-MM-DD'),
      totalCost: 100,
    });
    await billingCustomerRepository.create({
      customerId: 'customer-123',
      tenantId: savedSubscription.subscriberId,
    });
  }
});
