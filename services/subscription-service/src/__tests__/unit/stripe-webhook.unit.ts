/* eslint-disable @typescript-eslint/naming-convention */
import {expect} from '@loopback/testlab';
import sinon from 'sinon';
import {StripeWebhookService} from '../../services/stripe-webhook.service';
import {
  BillingCustomerRepository,
  InvoiceRepository,
  SubscriptionRepository,
} from '../../repositories';
import {IBillingService} from '../../types';
import {InvoiceStatus, StripeEventPayload, StripeEvents} from '@local/core';
import {UnitHelperService} from '../../services';
import {SubscriptionStatus} from '@sourceloop/ctrl-plane-subscription-service';
import {BillingCustomer, Invoice} from '../../models';
import Stripe from 'stripe';

describe('StripeWebhookService', () => {
  let stripeWebhookService: StripeWebhookService;
  let invoiceRepoStub: sinon.SinonStubbedInstance<InvoiceRepository>;
  let billingServiceStub: sinon.SinonStubbedInstance<IBillingService>;
  let billingCustomerRepoStub: sinon.SinonStubbedInstance<BillingCustomerRepository>;
  let subscriptionRepoStub: sinon.SinonStubbedInstance<SubscriptionRepository>;
  let unitHelperService: sinon.SinonStubbedInstance<UnitHelperService>;

  beforeEach(() => {
    invoiceRepoStub = sinon.createStubInstance(InvoiceRepository<Invoice>);
    billingServiceStub = {
      getSubscription: sinon.stub(),
    } as unknown as sinon.SinonStubbedInstance<IBillingService>;
    unitHelperService = {
      convertToMinorUnit: sinon.stub(),
      convertToMajorUnit: sinon.stub(),
    } as unknown as sinon.SinonStubbedInstance<UnitHelperService>;

    billingCustomerRepoStub = sinon.createStubInstance(
      BillingCustomerRepository<BillingCustomer>,
    );
    subscriptionRepoStub = sinon.createStubInstance(SubscriptionRepository);

    stripeWebhookService = new StripeWebhookService(
      billingServiceStub,
      subscriptionRepoStub,
      billingCustomerRepoStub as unknown as BillingCustomerRepository<BillingCustomer>,
      invoiceRepoStub as unknown as InvoiceRepository<Invoice>,
      unitHelperService,
    );
  });

  afterEach(() => {
    sinon.restore(); // Restore all mocks
  });

  it('should handle INVOICE_CREATED event', async () => {
    (billingCustomerRepoStub.findOne as sinon.SinonStub).resolves({
      id: 'cust_123',
    });
    (invoiceRepoStub.create as sinon.SinonStub).resolves();
    (billingServiceStub.getSubscription as sinon.SinonStub).resolves({
      id: 'sub_123',
      status: 'active',
    });
    const event: StripeEventPayload = {
      type: StripeEvents.INVOICE_CREATED,
      data: {
        object: {
          id: 'inv_123',
          customer: 'cus_123',
          parent: {
            subscription_details: {
              subscription: 'sub_123',
            },
          },
          amount_due: 5000,
          currency: 'usd',
          status: 'open',
          total_tax_amounts: [{amount: 500}],
          total_discount_amounts: [{amount: 200}],
          period_start: 1700000000,
          period_end: 1705000000,
          due_date: 1706000000,
          number: 'INV-001',
          hosted_invoice_url: 'https://invoice.url',
          lines: {
            data: [
              {
                period: {
                  start: 1700000000,
                  end: 1705000000,
                },
              },
            ],
          },
        } as Stripe.Invoice,
      },
    };

    await stripeWebhookService.handleWebhookEvent(event);

    sinon.assert.calledOnce(billingCustomerRepoStub.findOne as sinon.SinonSpy);
    sinon.assert.calledOnce(invoiceRepoStub.create as sinon.SinonSpy);
  });

  it('should handle SUBSCRIPTION_UPDATE event and update subscription', async () => {
    // Arrange: stub updateAll to resolve
    (subscriptionRepoStub.updateAll as sinon.SinonStub).resolves();

    const event = {
      type: StripeEvents.SUBSCRIPTION_UPDATE,
      data: {
        object: {
          id: 'sub_123',
          status: 'active',
          billing_cycle_anchor: 1700000000,
          items: {
            data: [
              {
                plan: {
                  interval: 'day',
                  interval_count: 1,
                },
              },
            ],
          },
        },
      },
    } as unknown as StripeEventPayload;

    // Act
    const res = await stripeWebhookService.handleWebhookEvent(event);

    // Assert
    sinon.assert.calledOnce(subscriptionRepoStub.updateAll as sinon.SinonSpy);
    // ensure updateAll was called with ACTIVE status and the expected where clause
    sinon.assert.calledWithMatch(
      subscriptionRepoStub.updateAll as sinon.SinonStub,
      {status: SubscriptionStatus.ACTIVE},
      {externalSubscriptionId: 'sub_123'},
    );
    expect(res.event).to.equal(StripeEvents.SUBSCRIPTION_UPDATE);
    expect(res.success).to.be.true();
  });

  it('should handle INVOICE_PAID event and update invoice status to PAID', async () => {
    // Arrange
    (invoiceRepoStub.updateAll as sinon.SinonStub).resolves();

    const event: StripeEventPayload = {
      type: StripeEvents.INVOICE_PAID,
      data: {
        object: {
          id: 'inv_456',
        } as Stripe.Invoice,
      },
    };

    // Act
    const res = await stripeWebhookService.handleWebhookEvent(event);

    // Assert
    sinon.assert.calledOnce(invoiceRepoStub.updateAll as sinon.SinonSpy);
    sinon.assert.calledWithMatch(
      invoiceRepoStub.updateAll as sinon.SinonStub,
      {invoiceStatus: InvoiceStatus.PAID},
      {invoiceId: 'inv_456'},
    );
    expect(res.event).to.equal(StripeEvents.INVOICE_PAID);
    expect(res.success).to.be.true();
  });

  it('should return sendEmail info for INVOICE_CREATED when subscription is incomplete', async () => {
    // Arrange
    (billingCustomerRepoStub.findOne as sinon.SinonStub).resolves({
      id: 'cust_123',
      tenantId: 'tenant_abc',
    });
    (invoiceRepoStub.create as sinon.SinonStub).resolves();
    (billingServiceStub.getSubscription as sinon.SinonStub).resolves({
      id: 'sub_123',
      status: 'incomplete',
    });

    const event: StripeEventPayload = {
      type: StripeEvents.INVOICE_CREATED,
      data: {
        object: {
          id: 'inv_789',
          customer: 'cus_123',
          parent: {
            subscription_details: {
              subscription: 'sub_123',
            },
          },
          amount_due: 5000,
          currency: 'usd',
          status: 'open',
          total_tax_amounts: [{amount: 500}],
          total_discount_amounts: [{amount: 200}],
          period_start: 1700000000,
          period_end: 1705000000,
          due_date: 1706000000,
          number: 'INV-002',
          hosted_invoice_url: 'https://invoice.url/789',
          customer_email: '<EMAIL>',
          lines: {
            data: [
              {
                period: {
                  start: 1700000000,
                  end: 1705000000,
                },
              },
            ],
          },
        } as Stripe.Invoice,
      },
    };

    // Act
    const res = await stripeWebhookService.handleWebhookEvent(event);

    // Assert: invoice created + sendEmail info returned
    sinon.assert.calledOnce(billingCustomerRepoStub.findOne as sinon.SinonSpy);
    sinon.assert.calledOnce(invoiceRepoStub.create as sinon.SinonSpy);
    expect(res.event).to.equal(StripeEvents.INVOICE_CREATED);
    expect(res.success).to.be.true();
    expect(res.sendEmail).to.be.true();
    expect(res.message).to.equal('https://invoice.url/789');
    expect(res.email).to.equal('<EMAIL>');
    expect(res.userId).to.equal('tenant_abc');
  });

  it('should return success:false for INVOICE_CREATED when billing customer not found', async () => {
    // Arrange: no billing customer
    (billingCustomerRepoStub.findOne as sinon.SinonStub).resolves(null);

    const event: StripeEventPayload = {
      type: StripeEvents.INVOICE_CREATED,
      data: {
        object: {
          id: 'inv_missing_cust',
          customer: 'cus_missing',
        } as Stripe.Invoice,
      },
    };

    // Act
    const res = await stripeWebhookService.handleWebhookEvent(event);

    // Assert
    expect(res.event).to.equal(StripeEvents.INVOICE_CREATED);
    expect(res.success).to.be.false();
  });
});
