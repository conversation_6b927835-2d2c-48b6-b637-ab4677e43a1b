import {model, property, belongsTo, hasMany} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {
  BillingCycle,
  Currency,
  PlanSizes,
} from '@sourceloop/ctrl-plane-subscription-service';
import {ConfigureDevice} from './configure-devices.model';
import {PlanHistory} from './plan-history.model';
import {PlanStatus, PlanTierType} from '@local/core';

/**
 * Represents a subscription Plan entity.
 *
 * @remarks
 * This model defines subscription plans with various attributes including pricing,
 * tier, status, and related entities such as billing cycles, currency, device
 * configurations, and plan size. It also tracks historical versions of the plan
 * through the planHistories relation.
 *
 * @property {string} id - Unique identifier for the plan (auto-generated).
 * @property {string} name - Name of the plan.
 * @property {string} [description] - Optional description for the plan.
 * @property {PlanTierType} tier - Tier category of the plan (e.g., premium, standard).
 * @property {number} price - Base price of the plan.
 * @property {object} [metaData] - Optional metadata related to the plan.
 * @property {PlanStatus} status - Current status of the plan (e.g., active, inactive).
 * @property {string} version - Current version string of the plan.
 * @property {boolean} allowedUnlimitedUsers - Whether the plan allows unlimited users.
 * @property {number} [costPerUser] - Additional cost per user if unlimited users are not allowed.
 * @property {string} billingCycleId - Foreign key referencing the BillingCycle entity.
 * @property {string} currencyId - Foreign key referencing the Currency entity.
 * @property {string} configureDeviceId - Foreign key referencing the ConfigureDevice entity.
 * @property {string} planSizeId - Foreign key referencing the PlanSizes entity.
 * @property {PlanHistory[]} planHistories - One-to-many relation to track historical plan versions.
 */
@model({
  name: 'plans',
})
export class Plan extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id: string;

  @property({
    type: 'string',
    description: 'name of the plan',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
    description: 'description of the plan',
  })
  description?: string;

  @property({
    type: 'string',
    required: true,
    description: 'Tier of the plan.',
    jsonSchema: {
      enum: Object.values(PlanTierType),
    },
  })
  tier: PlanTierType;

  @property({
    type: 'number',
    required: true,
  })
  price: number;

  @property({
    type: 'object',
    name: 'meta_data',
    description: 'Meta data of the plan',
  })
  metaData?: object;

  @property({
    type: 'number',
    description: 'Status of the plan.',
    required: true,
    jsonSchema: {
      enum: Object.values(PlanStatus).filter(v => typeof v === 'number'),
    },
  })
  status: PlanStatus;

  @property({
    type: 'string',
    description: 'current version of the plan.',
    required: true,
  })
  version: string;

  @property({
    type: 'boolean',
    description: 'Indicates if the plan allows unlimited users.',
    name: 'allowed_unlimited_users',
  })
  allowedUnlimitedUsers: boolean;

  @property({
    type: 'number',
    description: 'price added per user for the plan.',
    name: 'cost_per_user',
  })
  costPerUser?: number;

  @property({
    type: 'string',
    description: 'product reference ID of the plan.',
    required: true,
    name: 'product_ref_id',
  })
  productRefId: string;

  @belongsTo(
    () => BillingCycle,
    {
      keyTo: 'id',
    },
    {
      name: 'billing_cycle_id',
    },
  )
  billingCycleId: string;

  @belongsTo(() => Currency, undefined, {
    name: 'currency_id',
  })
  currencyId: string;

  @belongsTo(
    () => ConfigureDevice,
    {keyTo: 'id'},
    {
      name: 'configure_devices_id',
    },
  )
  configureDeviceId: string;

  @belongsTo(
    () => PlanSizes,
    {keyTo: 'id'},
    {
      name: 'plan_size_id',
    },
  )
  planSizeId: string;

  @hasMany(() => PlanHistory, {
    keyTo: 'planId',
  })
  planHistories: PlanHistory[];

  constructor(data?: Partial<Plan>) {
    super(data);
  }
}

export interface PlanRelations {
  billingCycle?: BillingCycle;
  currency?: Currency;
  configureDevice?: ConfigureDevice;
  planSize?: PlanSizes;
  planHistories?: PlanHistory[];
}

export type PlanWithRelations = Plan;
