import {Client, expect} from '@loopback/testlab';
import {UserTenantServiceApplication} from '../..';
import {setupApplication, getToken} from './test-helper';
import {PermissionKey} from '@local/core';
import sinon from 'sinon';
import {
  UserRepository,
  UserViewRepository,
  RoleRepository,
  TenantRepository,
  UserTenantRepository,
} from '@sourceloop/user-tenant-service';
import {createMockUserTenant} from '../unit/test-data';

describe('UserController (acceptance)', () => {
  let app: UserTenantServiceApplication;
  let client: Client;
  let userViewRepositoryStub: sinon.SinonStubbedInstance<UserViewRepository>;
  let userRepositoryStub: sinon.SinonStubbedInstance<UserRepository>;
  let roleRepositoryStub: sinon.SinonStubbedInstance<RoleRepository>;
  let tenantRepositoryStub: sinon.SinonStubbedInstance<TenantRepository>;
  let userTenantRepositoryStub: sinon.SinonStubbedInstance<UserTenantRepository>;
  let getCurrentUserStub: sinon.SinonStub;

  const tenantId = '3f09f2e5-b6e7-4e5d-bb71-bf3fcb7a1f69';

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
  });

  after(async () => {
    sinon.restore();
    await app.stop();
  });

  beforeEach(() => {
    sinon.restore();

    userViewRepositoryStub = sinon.createStubInstance(UserViewRepository);
    app.bind('repositories.UserViewRepository').to(userViewRepositoryStub);

    userRepositoryStub = sinon.createStubInstance(UserRepository);
    app.bind('repositories.UserRepository').to(userRepositoryStub);

    roleRepositoryStub = sinon.createStubInstance(RoleRepository);
    app.bind('repositories.RoleRepository').to(roleRepositoryStub);

    tenantRepositoryStub = sinon.createStubInstance(TenantRepository);
    app.bind('repositories.TenantRepository').to(tenantRepositoryStub);

    userTenantRepositoryStub = sinon.createStubInstance(UserTenantRepository);
    app.bind('repositories.UserTenantRepository').to(userTenantRepositoryStub);

    getCurrentUserStub = sinon.stub().resolves({
      id: 'test',
      username: 'test',
      userTenantId: 'test',
      tenantId: tenantId,
      permissions: [],
    });
    app.bind('getCurrentUser').to(getCurrentUserStub);
  });

  it('invokes GET /tenants/{id}/users/count and returns correct user count', async () => {
    userViewRepositoryStub.count.resolves({count: 2});
    const token = getToken([PermissionKey.ViewTenantUser]);
    const res = await client
      .get(`/tenants/${tenantId}/users/count`)
      .set('Authorization', token)
      .expect(200);
    expect(res.body).to.have.property('count', 2);
  });

  it('invokes GET /tenants/{id}/users/count for tenant with no users returns 0', async () => {
    userViewRepositoryStub.count.resolves({count: 0});
    const token = getToken([PermissionKey.ViewTenantUser]);
    const res = await client
      .get(`/tenants/${tenantId}/users/count`)
      .set('Authorization', token)
      .expect(200);
    expect(res.body).to.have.property('count', 0);
  });

  it('returns 401 for GET /tenants/{id}/users/count without token', async () => {
    await client.get(`/tenants/${tenantId}/users/count`).expect(401);
  });

  it('returns 401 for GET /tenants/{id}/users/count without authorization token', async () => {
    await client.get(`/tenants/${tenantId}/users/count`).expect(401);
  });

  it('returns 403 for GET /tenants/{id}/users/count with invalid permissions', async () => {
    const token = getToken([]);
    await client
      .get(`/tenants/${tenantId}/users/count`)
      .set('Authorization', token)
      .expect(403);
  });

  it('returns 401 for POST /tenants/{id}/users/bulk without token', async () => {
    const users = [
      {
        firstName: 'Bulk',
        username: 'bulk1',
        email: '<EMAIL>',
        roleId: 'r1',
      },
    ];
    await client
      .post(`/tenants/${tenantId}/users/bulk`)
      .send(users)
      .expect(401);
  });

  it('returns 403 for POST /tenants/{id}/users/bulk without permission', async () => {
    const token = getToken([]);
    const users = [
      {
        firstName: 'Bulk',
        username: 'bulk1',
        email: '<EMAIL>',
        roleId: 'r1',
      },
    ];
    await client
      .post(`/tenants/${tenantId}/users/bulk`)
      .set('Authorization', token)
      .send(users)
      .expect(403);
  });
  // PATCH /tenants/{tenantId}/users/{userId}/status - success
  it('PATCH /tenants/{tenantId}/users/{userId}/status updates status', async () => {
    // Use the type-safe helper for mock creatio
    userTenantRepositoryStub.findOne.resolves(
      createMockUserTenant({
        id: 'ut1',
        userId: 'u1',
        tenantId,
        roleId: 'r1',
        userLevelPermissions: [],
        userGroups: undefined,
        userInvitations: [],
        createdOn: new Date(),
        createdBy: 'system',
        status: 1,
      }),
    );
    userTenantRepositoryStub.updateById.resolves();
    const token = getToken([PermissionKey.UpdateTenantUser]);
    const statusPayload = {status: 1};
    await client
      .patch(`/tenants/${tenantId}/users/u1/status`)
      .set('Authorization', token)
      .send(statusPayload)
      .expect(204);
  });

  // PATCH /tenants/{tenantId}/users/{userId}/status - not found
  it('PATCH /tenants/{tenantId}/users/{userId}/status returns 404 if user not found', async () => {
    userTenantRepositoryStub.findOne.resolves(null);
    const token = getToken([PermissionKey.UpdateTenantUser]);
    const statusPayload = {status: 1};
    const res = await client
      .patch(`/tenants/${tenantId}/users/1/status`)
      .set('Authorization', token)
      .send(statusPayload);
    expect(res.body.error.message.message).to.eql('User with id 1 not found');
  });

  // PATCH /tenants/{tenantId}/users/{userId}/status - invalid status
  it('PATCH /tenants/{tenantId}/users/{userId}/status returns 422 for invalid status', async () => {
    const token = getToken([PermissionKey.UpdateTenantUser]);
    const statusPayload = {status: 99};
    await client
      .patch(`/tenants/${tenantId}/users/u1/status`)
      .set('Authorization', token)
      .send(statusPayload)
      .expect(422);
  });

  // PATCH /tenants/{tenantId}/users/{userId}/status - permission denied
  it('PATCH /tenants/{tenantId}/users/{userId}/status returns 403 if no permission', async () => {
    const token = getToken([]);
    const statusPayload = {status: 1};
    await client
      .patch(`/tenants/${tenantId}/users/u1/status`)
      .set('Authorization', token)
      .send(statusPayload)
      .expect(403);
  });

  // PATCH /tenants/{tenantId}/users/{userId}/status - no token
  it('PATCH /tenants/{tenantId}/users/{userId}/status returns 401 if no token', async () => {
    const statusPayload = {status: 1};
    await client
      .patch(`/tenants/${tenantId}/users/u1/status`)
      .send(statusPayload)
      .expect(401);
  });

  // POST /tenants/{id}/users/bulk - invalid payload
  it('POST /tenants/{id}/users/bulk returns 422 for invalid payload', async () => {
    const token = getToken([PermissionKey.CreateTenantUser]);
    await client
      .post(`/tenants/${tenantId}/users/bulk`)
      .set('Authorization', token)
      .send({foo: 'bar'})
      .expect(422);
  });
});
