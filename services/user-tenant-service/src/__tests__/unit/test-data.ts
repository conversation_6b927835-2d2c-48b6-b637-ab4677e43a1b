import {DataObject, Model} from '@loopback/repository';
import {
  Role,
  RoleRelations,
  Tenant,
  TenantRelations,
  User,
  UserRelations,
  UserTenant,
  UserTenantRelations,
} from '@sourceloop/user-tenant-service';

export const fakeTenant: Tenant & TenantRelations = {
  id: 'tenant-1',
  name: 'TenantName',
  createdOn: new Date(),
  createdBy: 'user-1',
  status: 1,
  tenantConfigs: [
    {
      id: 'config-1',
      key: 'sampleKey',
      value: 'sampleValue',
      tenantId: 'tenant-1',
      tenant: {id: 'tenant-1'}, // required by TenantConfigWithRelations
    },
  ],
  userTenants: [],
  getId: () => 'tenant-1',
  getIdObject: () => ({}),
  toJSON: () => ({}),
  toObject: () => ({}),
} as unknown as Tenant & TenantRelations;

export const fakeUser: User<DataObject<Model>> & UserRelations = {
  id: 'user-1',
  username: 'testuser',
  email: '<EMAIL>',
  authClientIds: [1],
  defaultTenantId: 'tenant-1',
  createdBy: 'user-1',
  firstName: 'Test',
  lastName: 'User',
  middleName: '',
  phone: '',
  photoUrl: '',
  gender: undefined,
  dob: new Date(),
  designation: '',
  deleted: false,
  deletedBy: '',
  deletedOn: undefined,
  createdOn: new Date(),
  defaultTenant: {},
  credentials: {user: {}},
} as unknown as User<DataObject<Model>> & UserRelations;

export const fakeRole: Role & RoleRelations = {
  id: 'role-1',
  name: 'RoleName',
  tenantId: 'tenant-1',
  userTenants: [],
  createdOn: new Date(),
  createdBy: 'user-1',
  getId: () => 'role-1',
  getIdObject: () => ({}),
  toJSON: () => ({}),
  toObject: () => ({}),
} as unknown as Role & RoleRelations;

import {UserDto} from '@sourceloop/user-tenant-service';

export const fakeUserDto = {
  id: 'user-1',
  username: 'testuser',
  email: '<EMAIL>',
  roleId: 'role-1',
  authClientIds: '{1}',
  defaultTenantId: 'tenant-1',
  firstName: 'Test',
  lastName: 'User',
  middleName: '',
  phone: '',
  photoUrl: '',
  gender: undefined,
  dob: new Date(),
  designation: '',
  credentials: {
    authProvider: 'mock',
    userId: 'user-1',
    password: 'mock',
    salt: 'mock',
    verificationCode: 'mock',
    verificationCodeExpiry: new Date(),
    mfaEnabled: false,
    getId: () => 'cred-1',
    getIdObject: () => ({}),
    toJSON: () => ({}),
    toObject: () => ({}),
  },
  userTenants: [],
  getId: () => 'user-1',
  getIdObject: () => ({}),
  toJSON: () => ({}),
  toObject: () => ({}),
} as UserDto;

// UserTenant test data
export const fakeUserTenant = {
  id: 'ut-1',
  userId: 'user-1',
  tenantId: 'tenant-1',
  roleId: 'role-1',
  status: 1,
  locale: 'en',
  userLevelPermissions: [],
  createdOn: new Date(),
  createdBy: 'user-1',
  userGroups: [
    {
      id: 'group-1',
      groupId: 'group-1',
      userTenantId: 'ut-1',
      getId: () => 'group-1',
      getIdObject: () => ({}),
      toJSON: () => ({}),
      toObject: () => ({}),
    },
  ],
  userInvitations: [],
  getId: () => 'ut-1',
  getIdObject: () => ({}),
  toJSON: () => ({}),
  toObject: () => ({}),
} as UserTenant;

export const fakeUserTenantAlt = {
  id: 'ut-2',
  userId: 'user-2',
  tenantId: 'tenant-2',
  roleId: 'role-2',
  status: 1,
  locale: 'en',
  userLevelPermissions: [],
  createdOn: new Date(),
  createdBy: 'user-2',
  userGroups: [
    {
      id: 'group-2',
      groupId: 'group-2',
      userTenantId: 'ut-2',
      getId: () => 'group-2',
      getIdObject: () => ({}),
      toJSON: () => ({}),
      toObject: () => ({}),
    },
  ],
  userInvitations: [],
  getId: () => 'ut-2',
  getIdObject: () => ({}),
  toJSON: () => ({}),
  toObject: () => ({}),
} as UserTenant;

/**
 * Creates a mock UserTenant object with type safety.
 * @param overrides Properties to override for specific test cases.
 */
export function createMockUserTenant(
  overrides: Partial<UserTenant & UserTenantRelations> = {},
): UserTenant & UserTenantRelations {
  return {
    id: 'ut1',
    userId: 'u1',
    tenantId: 't1',
    roleId: 'r1',
    status: 1,
    createdOn: new Date(),
    createdBy: 'system',
    userLevelPermissions: [],
    userGroups: [],
    userInvitations: [],
    getId: () => 'ut1',
    getIdObject: () => ({}),
    toJSON: () => ({}),
    toObject: () => ({}),
    ...overrides,
  } as UserTenant & UserTenantRelations;
}
