import {expect, sinon} from '@loopback/testlab';
import {UserOperationsHelperService} from '../../services/user-operations-helper.service';
import {
  RoleRepository,
  UserRepository,
  TenantRepository,
  UserTenantRepository,
  User,
  UserRelations,
} from '@sourceloop/user-tenant-service';
import {DataObject, Model} from '@loopback/repository';
import {UpdateTenantUserStatusRequestDto} from '../../models/dto/update-tenant-user-status-req.dto.model';
import {HttpErrors} from '@loopback/rest';

import {
  fakeTenant,
  fakeUser,
  fakeRole,
  fakeUserDto,
  fakeUserTenant,
} from './test-data';

describe('UserOperationsHelperService (unit)', () => {
  let service: UserOperationsHelperService;
  let roleRepo: sinon.SinonStubbedInstance<RoleRepository>;
  let userRepo: sinon.SinonStubbedInstance<UserRepository>;
  let tenantRepo: sinon.SinonStubbedInstance<TenantRepository>;
  let userTenantRepo: sinon.SinonStubbedInstance<UserTenantRepository>;
  let getCurrentUser: sinon.SinonStub;
  const tenantId = 'tenant-1';
  const userId = 'user-1';

  beforeEach(() => {
    roleRepo = sinon.createStubInstance(RoleRepository);
    userRepo = sinon.createStubInstance(UserRepository);
    tenantRepo = sinon.createStubInstance(TenantRepository);
    userTenantRepo = sinon.createStubInstance(UserTenantRepository);
    getCurrentUser = sinon.stub();

    service = new UserOperationsHelperService(
      roleRepo,
      userRepo,
      tenantRepo,
      userTenantRepo,
      getCurrentUser,
    );
  });

  afterEach(() => {
    sinon.restore();
  });

  it('should throw conflict error if duplicate users found', async () => {
    tenantRepo.findById.resolves(fakeTenant);
    getCurrentUser.resolves({id: userId, tenantId, authClientIds: [1]});
    userRepo.findById.resolves(fakeUser);
    userRepo.find.resolves([fakeUser]);
    const duplicateDto = Object.assign({}, fakeUserDto, {
      email: fakeUser.email,
    });
    await expect(
      service.createMany([duplicateDto], tenantId),
    ).to.be.rejectedWith(HttpErrors.Conflict);
  });

  it('should throw forbidden if tenant access not allowed', async () => {
    tenantRepo.findById.resolves(fakeTenant);
    getCurrentUser.resolves({
      id: userId,
      tenantId: 'other-tenant',
      authClientIds: [1],
    });
    userRepo.findById.resolves(fakeUser);
    const invalidDto = Object.assign({}, fakeUserDto);
    await expect(service.createMany([invalidDto], tenantId)).to.be.rejectedWith(
      HttpErrors.Forbidden,
    );
  });

  it('should throw bad request for invalid email', async () => {
    tenantRepo.findById.resolves(fakeTenant);
    getCurrentUser.resolves({id: userId, tenantId, authClientIds: [1]});
    userRepo.findById.resolves(fakeUser);
    const invalidDto = Object.assign({}, fakeUserDto, {email: 'invalid-email'});
    await expect(service.createMany([invalidDto], tenantId)).to.be.rejectedWith(
      HttpErrors.BadRequest,
    );
  });

  it('should throw not found if userTenant not found in updateTenantUserStatus', async () => {
    const updateDto = new UpdateTenantUserStatusRequestDto({status: 1});
    userTenantRepo.findOne.resolves(null);
    await expect(
      service.updateTenantUserStatus(tenantId, userId, updateDto),
    ).to.be.rejectedWith(HttpErrors.NotFound);
  });
});
// Additional tests for uncovered lines in UserOperationsV2Service

describe('UserOperationsV2Service uncovered lines', () => {
  let service: UserOperationsHelperService;
  let roleRepo: sinon.SinonStubbedInstance<RoleRepository>;
  let userRepo: sinon.SinonStubbedInstance<UserRepository>;
  let tenantRepo: sinon.SinonStubbedInstance<TenantRepository>;
  let userTenantRepo: sinon.SinonStubbedInstance<UserTenantRepository>;
  let getCurrentUser: sinon.SinonStub;
  const tenantId = 'tenant-1';
  const userId = 'user-1';
  const roleId = 'role-1';

  beforeEach(() => {
    roleRepo = sinon.createStubInstance(RoleRepository);
    userRepo = sinon.createStubInstance(UserRepository);
    tenantRepo = sinon.createStubInstance(TenantRepository);
    userTenantRepo = sinon.createStubInstance(UserTenantRepository);
    getCurrentUser = sinon.stub();

    service = new UserOperationsHelperService(
      roleRepo,
      userRepo,
      tenantRepo,
      userTenantRepo,
      getCurrentUser,
    );
  });

  it('should throw if userRepo.findById throws (line 65)', async () => {
    tenantRepo.findById.resolves(fakeTenant);
    getCurrentUser.resolves({id: userId, tenantId});
    userRepo.findById.rejects(new Error('User not found'));
    await expect(
      service.createMany([Object.assign({}, fakeUserDto)], tenantId),
    ).to.be.rejectedWith('User not found');
  });

  it('should create multiple users and userTenants (lines 110-147)', async () => {
    tenantRepo.findById.resolves(fakeTenant);
    getCurrentUser.resolves({id: userId, tenantId, authClientIds: [1]});
    // Fix: Use User class instance to satisfy required methods
    // Fix: Assign UserRelations properties using type assertion to User<DataObject<Model>> & UserRelations
    const userInstance = new User({...fakeUser, id: userId}) as User<
      DataObject<Model>
    > &
      UserRelations;
    userInstance.defaultTenant = fakeUser.defaultTenant;
    userInstance.credentials = fakeUser.credentials;
    userRepo.findById.resolves(userInstance);
    userRepo.find.resolves([]);
    const userDtos = [
      Object.assign({}, fakeUserDto, {
        username: 'userA',
        email: '<EMAIL>',
      }),
      Object.assign({}, fakeUserDto, {
        username: 'userB',
        email: '<EMAIL>',
      }),
    ];
    userRepo.createAll.resolves([
      new User({
        ...fakeUser,
        id: 'userA',
        username: 'userA',
        email: '<EMAIL>',
      }),
      new User({
        ...fakeUser,
        id: 'userB',
        username: 'userB',
        email: '<EMAIL>',
      }),
    ]);
    roleRepo.findById.resolves(fakeRole);
    userTenantRepo.findOne.resolves(null);
    userTenantRepo.createAll.resolves([
      Object.assign({}, fakeUserTenant, {
        userId: 'userA',
        tenantId,
        roleId,
        createdBy: userId,
      }),
      Object.assign({}, fakeUserTenant, {
        userId: 'userB',
        tenantId,
        roleId,
        createdBy: userId,
      }),
    ]);
    const result = await service.createMany(userDtos, tenantId);
    expect(result).to.have.length(2);
    sinon.assert.calledWith(userRepo.createAll, sinon.match.array);
  });
});
