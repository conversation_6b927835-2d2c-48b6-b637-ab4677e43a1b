// Copyright (c) SourceLoop. All rights reserved.
// Unit tests for TenantUserV2Controller

import {expect} from '@loopback/testlab';
import sinon from 'sinon';
import {UserController} from '../../../controllers/users.controller';
import {
  UserViewRepository,
  UserView,
  User,
  UserDto,
} from '@sourceloop/user-tenant-service';
import {UserOperationsHelperService} from '../../../services/user-operations-helper.service';
import {Count, Where} from '@loopback/repository';

describe('UserController (unit)', () => {
  let controller: UserController;
  let userViewRepository: sinon.SinonStubbedInstance<UserViewRepository>;
  let userOperationsService: sinon.SinonStubbedInstance<UserOperationsHelperService>;

  beforeEach(() => {
    userViewRepository = sinon.createStubInstance(UserViewRepository);
    userOperationsService = sinon.createStubInstance(
      UserOperationsHelperService,
    );
    controller = new UserController(userViewRepository, userOperationsService);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('count', () => {
    it('returns user count with combined where clause', async () => {
      const tenantId = 'tenant-1';
      const where: Where<UserView> = {email: '<EMAIL>'};
      const expectedWhere = {...where, tenantId};
      const mockCount: Count = {count: 2};

      userViewRepository.count.resolves(mockCount);

      const result = await controller.count(tenantId, where);

      expect(result).to.deepEqual(mockCount);
      sinon.assert.calledWith(userViewRepository.count, expectedWhere);
    });

    it('returns user count with tenant filter only when no where clause provided', async () => {
      const tenantId = 'tenant-1';
      const expectedWhere = {tenantId};
      const mockCount: Count = {count: 5};

      userViewRepository.count.resolves(mockCount);

      const result = await controller.count(tenantId);

      expect(result).to.deepEqual(mockCount);
      sinon.assert.calledWith(userViewRepository.count, expectedWhere);
    });
  });

  describe('createMany', () => {
    it('calls service to create users and returns created users', async () => {
      const tenantId = 'tenant-1';
      const users: UserDto[] = [
        {
          firstName: 'A',
          username: 'a',
          email: '<EMAIL>',
          roleId: 'r1',
        } as UserDto,
      ];
      const createdUsers: User[] = [
        {
          id: 'u1',
          firstName: 'A',
          lastName: 'Test',
          username: 'a',
          email: '<EMAIL>',
          userTenants: [],
          credentials: {},
          getId: () => 'u1',
          getIdObject: () => ({}),
          toJSON: () => ({}),
          toObject: () => ({}),
        } as unknown as User,
      ];

      userOperationsService.createMany.resolves(createdUsers);

      const result = await controller.createMany(tenantId, users);

      expect(result).to.deepEqual(createdUsers);
      sinon.assert.calledWith(
        userOperationsService.createMany,
        users,
        tenantId,
      );
      expect(users[0].defaultTenantId).to.equal(tenantId);
    });
  });

  describe('updateTenantUserStatus', () => {
    it('calls service to update tenant user status', async () => {
      const tenantId = 'tenant-1';
      const userId = 'user-1';

      // Mock UpdateTenantUserStatusRequestDto shape
      const bodyDto = {
        status: 1,
        toJSON: () => ({status: 1}),
        toObject: () => ({status: 1}),
      };

      await controller.updateTenantUserStatus(tenantId, userId, bodyDto);

      sinon.assert.calledWith(
        userOperationsService.updateTenantUserStatus,
        tenantId,
        userId,
        bodyDto,
      );
    });
  });
});
