import {model, Model, property} from '@loopback/repository';

/**
 * DTO for updating the status of a tenant user.
 *
 * Used in PATCH requests to update a user's status within a tenant.
 *
 * UserStatus mapping:
 * 0 = REGISTERED
 * 1 = ACTIVE
 * 2 = INACTIVE
 *
 * Example:
 * {
 *   "status": 1
 * }
 */
@model({
  description:
    'DTO for updating the status of a tenant user. UserStatus mapping: 0=REGISTERED, 1=ACTIVE, 2=INACTIVE',
})
export class UpdateTenantUserStatusRequestDto extends Model {
  @property({
    type: 'number',
    required: true,
    jsonSchema: {
      enum: [0, 1, 2],
      description: 'UserStatus: 0=REGISTERED, 1=ACTIVE, 2=INACTIVE',
      errorMessage: {
        enum: 'Status must be a valid UserStatus integer value: 0=REGISTERED, 1=ACTIVE, 2=INACTIVE',
      },
    },
  })
  status!: number;

  constructor(data?: Partial<UpdateTenantUserStatusRequestDto>) {
    super(data);
  }
}
