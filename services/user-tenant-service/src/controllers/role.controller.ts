import {PermissionKey} from '@local/core';
import {service} from '@loopback/core';
import {get, getModelSchemaRef, param} from '@loopback/openapi-v3';
import {Filter, FilterBuilder} from '@loopback/repository';
import {OPERATION_SECURITY_SPEC} from '@sourceloop/core';
import {Role} from '@sourceloop/user-tenant-service';
import {authenticate} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {RoleHelperService} from '../services/role-helper.service';

const basePath = 'tenants/{id}/roles/all';

export class RoleController {
  constructor(
    @service(RoleHelperService)
    public roleHelperService: RoleHelperService,
  ) {
    this.roleHelperService = roleHelperService;
  }

  @authenticate('bearer', {passReqToCallback: true})
  @authorize({
    permissions: [PermissionKey.ViewRoles],
  })
  @get(`${basePath}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'Array of Role model instances',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(Role),
            },
          },
        },
      },
    },
  })
  async findAllRoles(
    @param.path.string('id') tenantId: string,
    @param.filter(Role) filter?: Filter<Role>,
  ): Promise<Role[]> {
    const filterBuilder = new FilterBuilder<Role>(filter);
    const existingWhere = filter?.where ?? {};
    filterBuilder.where({and: [existingWhere, {tenantId}]});
    return this.roleHelperService.findAllRoles(filterBuilder.build());
  }
}
