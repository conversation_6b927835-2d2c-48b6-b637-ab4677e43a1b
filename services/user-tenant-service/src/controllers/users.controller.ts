import {PermissionKey} from '@local/core';
import {intercept, service} from '@loopback/core';
import {Count, CountSchema, repository, Where} from '@loopback/repository';
import {
  get,
  getModelSchemaRef,
  param,
  post,
  requestBody,
  patch,
} from '@loopback/rest';
import {
  CONTENT_TYPE,
  OPERATION_SECURITY_SPEC,
  STATUS_CODE,
} from '@sourceloop/core';
import {
  UserTenantServiceKey,
  User,
  UserDto,
  UserView,
  UserViewRepository,
} from '@sourceloop/user-tenant-service';
import {STRATEGY, authenticate} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {UserOperationsHelperService} from '../services/user-operations-helper.service';
import {UpdateTenantUserStatusRequestDto} from '../models/dto/update-tenant-user-status-req.dto.model';
const baseUrl = '/tenants/{id}/users';

/**
 * Controller for managing tenant users.
 *
 * Provides endpoints for:
 * - Counting users in a tenant with flexible filtering.
 * - Bulk creation of users for a tenant.
 * - Updating the status of a tenant user.
 *
 * All endpoints require authentication and appropriate permissions.
 */
@intercept(UserTenantServiceKey.TenantInterceptorInterceptor)
export class UserController {
  /**
   * Creates an instance of TenantUserV2Controller.
   * @param userTenantRepository - The repository for user-tenant relations.
   */
  constructor(
    @repository(UserViewRepository)
    readonly userViewRepository: UserViewRepository,
    @service(UserOperationsHelperService)
    protected userOperationsHelperService: UserOperationsHelperService,
  ) {}

  /**
   * Retrieves the count of users for a given tenant.
   *
   * @remarks
   * This endpoint returns the number of users associated with the specified tenant ID.
   * Requires bearer token authentication and appropriate permissions.
   *
   * @param id - The ID of the tenant.
   * @returns The count of users for the tenant.
   *
   * @response 200 - Successfully retrieved user count.
   * @security BearerToken
   */
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @authorize({
    permissions: [PermissionKey.ViewTenantUser],
  })
  @get(`${baseUrl}/count`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Object containing user count',
        content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
      },
    },
  })
  async count(
    @param.path.string('id') id: string,
    @param.where(UserView)
    where?: Where<UserView>,
  ): Promise<Count> {
    const whereObj = {
      ...where,
      tenantId: id,
    };
    return this.userViewRepository.count(whereObj);
  }

  /**
   * Bulk create users in a tenant.
   *
   * Creates multiple users and associates them with the specified tenant.
   * Handles duplicate user detection and returns created users or conflict details.
   *
   * @param id - The ID of the tenant.
   * @param users - Array of UserDto objects to create.
   * @returns Array of created User objects.
   * @throws 409 Conflict if duplicate users are found.
   */
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @authorize({
    permissions: [PermissionKey.CreateTenantUser],
  })
  @post(`${baseUrl}/bulk`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of created User model instances',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(User)},
          },
        },
      },
      [STATUS_CODE.CONFLICT]: {
        description: 'Duplicate users found',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                error: {type: 'string'},
                duplicateUsers: {
                  type: 'array',
                  items: getModelSchemaRef(UserDto),
                },
              },
            },
          },
        },
      },
    },
  })
  async createMany(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'array',
            items: getModelSchemaRef(UserDto, {
              title: 'NewUserInTenantBulk',
              exclude: ['id', 'defaultTenantId'],
            }),
          },
        },
      },
    })
    users: UserDto[],
  ): Promise<User[]> {
    users.forEach(u => (u.defaultTenantId = id));
    return this.userOperationsHelperService.createMany(users, id);
  }

  /**
   * Update the status of a tenant user.
   *
   * Allows updating the status (REGISTERED, ACTIVE, INACTIVE) of a user within a tenant.
   * Requires authentication and UpdateTenantUser permission.
   *
   * @param tenantId - The ID of the tenant.
   * @param userId - The ID of the user.
   * @param body - DTO containing the new status.
   * @returns No content on success.
   * @throws 404 Not Found if the user is not found.
   */
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @authorize({
    permissions: [PermissionKey.UpdateTenantUser],
  })
  @patch('/tenants/{tenantId}/users/{userId}/status', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'TenantUser status PATCH success',
      },
    },
  })
  async updateTenantUserStatus(
    @param.path.string('tenantId') tenantId: string,
    @param.path.string('userId') userId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UpdateTenantUserStatusRequestDto, {
            title: 'UpdateTenantUserStatusRequestDto',
          }),
        },
      },
    })
    body: UpdateTenantUserStatusRequestDto,
  ): Promise<void> {
    await this.userOperationsHelperService.updateTenantUserStatus(
      tenantId,
      userId,
      body,
    );
  }
}
