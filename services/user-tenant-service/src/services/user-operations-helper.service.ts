// Copyright (c) 2023 Sourcefuse Technologies
//
// This software is released under the MIT License.
// https://opensource.org/licenses/MIT

import {BindingScope, Getter, inject, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {IAuthUserWithPermissions, UserStatus} from '@sourceloop/core';
import {
  RoleRepository,
  UserRepository,
  TenantRepository,
  UserTenantRepository,
  UserDto,
  User,
  Tenant,
} from '@sourceloop/user-tenant-service';
import {AuthenticationBindings} from 'loopback4-authentication';
import {UpdateTenantUserStatusRequestDto} from '../models/dto/update-tenant-user-status-req.dto.model';

/**
 * Service for advanced user operations in the User-Tenant context.
 *
 * Provides:
 * - Bulk user creation with duplicate detection and tenant association.
 * - Validation for user creation and email format.
 * - Status update for tenant users.
 *
 * All methods enforce tenant scoping and permission checks.
 */
@injectable({scope: BindingScope.TRANSIENT})
export class UserOperationsHelperService {
  /**
   * Constructs the UserOperationsV2Service.
   * @param roleRepository - Repository for roles.
   * @param userRepository - Repository for users.
   * @param tenantRepository - Repository for tenants.
   * @param userTenantRepository - Repository for user-tenant associations.
   * @param getCurrentUser - Getter for the current authenticated user.
   */
  constructor(
    @repository(RoleRepository) readonly roleRepository: RoleRepository,
    @repository(UserRepository) readonly userRepository: UserRepository,
    @repository(TenantRepository) readonly tenantRepository: TenantRepository,
    @repository(UserTenantRepository)
    readonly userTenantRepository: UserTenantRepository,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
  ) {}

  /**
   * Bulk creates users and their user-tenant associations in a single DB operation.
   * Validates input, checks for duplicates, and associates users with the tenant.
   *
   * @param userDtoArray - Array of UserDto objects to create.
   * @param tenantId - Tenant ID for all users.
   * @returns Promise resolving to an array of created User objects.
   * @throws 409 Conflict if duplicate users are found.
   */
  async createMany(userDtoArray: UserDto[], tenantId: string): Promise<User[]> {
    const tenant = await this.tenantRepository.findById(tenantId);
    const currentUserToken = await this.getCurrentUser();
    const currentUser = await this.userRepository.findById(
      currentUserToken.id ?? '',
    );

    // Validate and prepare user data
    await Promise.all(
      userDtoArray.map(async userDtoData => {
        await this._validateUserCreation(userDtoData, tenant);
        userDtoData.authClientIds = `{${(currentUser.authClientIds as unknown as number[]).join()}}`;
        userDtoData.defaultTenantId = tenantId;
        userDtoData.email = userDtoData.email.toLowerCase().trim();
        userDtoData.username = userDtoData.username.toLowerCase().trim();
      }),
    );

    // Check for existing users in bulk
    const usernames = userDtoArray.map(u => u.username);
    const emails = userDtoArray.map(u => u.email);
    const existingUsers = await this.userRepository.find({
      where: {
        or: [{username: {inq: usernames}}, {email: {inq: emails}}],
      },
    });

    // Map for quick lookup
    const existingUserMap = new Map<string, User>();
    for (const user of existingUsers) {
      existingUserMap.set(user.username, user);
      existingUserMap.set(user.email, user);
    }

    // If any user already exists, throw error with details
    const duplicateUsers = userDtoArray.filter(
      u => existingUserMap.has(u.username) || existingUserMap.has(u.email),
    );
    if (duplicateUsers.length > 0) {
      const error = new HttpErrors.Conflict(`User(s) already exist`);
      error.duplicateUsers = duplicateUsers.map(u => u.email);
      throw error;
    }

    // Prepare users to create (all are new at this point)
    const usersToCreate = userDtoArray;

    // Bulk create new users
    let createdUsers: User[] = [];
    if (usersToCreate.length > 0) {
      createdUsers = await this.userRepository.createAll(
        usersToCreate.map(userDtoData => ({
          firstName: userDtoData.firstName,
          lastName: userDtoData.lastName,
          middleName: userDtoData.middleName,
          username: userDtoData.username,
          email: userDtoData.email,
          phone: userDtoData.phone,
          authClientIds: userDtoData.authClientIds,
          photoUrl: userDtoData.photoUrl,
          gender: userDtoData.gender,
          dob: userDtoData.dob,
          designation: userDtoData.designation,
          defaultTenantId: userDtoData.defaultTenantId,
        })),
      );
    }

    const allUsers: User[] = [...createdUsers];

    // Prepare userTenant records
    const userTenantRecords: Array<{
      locale?: string;
      status: UserStatus;
      userId: string;
      roleId: string;
      tenantId: string;
    }> = [];
    await Promise.all(
      userDtoArray.map(async userDtoData => {
        const user = allUsers.find(
          u =>
            u.username === userDtoData.username ||
            u.email === userDtoData.email,
        );
        if (!user) return;
        const role = await this.roleRepository.findById(userDtoData.roleId);

        // Check if userTenant already exists
        const userTenantExists = await this.userTenantRepository.findOne({
          where: {
            userId: user.id,
            tenantId,
          },
        });
        if (userTenantExists) {
          // Optionally, skip or throw error
          return;
        }
        userTenantRecords.push({
          locale: userDtoData.locale,
          status: UserStatus.REGISTERED,
          userId: user.id,
          roleId: role.id ?? '',
          tenantId,
        });
      }),
    );

    // Bulk create userTenant records
    if (userTenantRecords.length > 0) {
      await this.userTenantRepository.createAll(userTenantRecords);
    }

    return allUsers;
  }

  /**
   * Validates user creation input and tenant access.
   * Ensures the current user has access to the tenant and email is valid.
   *
   * @param userDtoData - UserDto data to validate.
   * @param tenant - Tenant entity.
   * @throws 403 Forbidden if tenant access is not allowed.
   * @throws 400 BadRequest if email is invalid.
   */
  private async _validateUserCreation(userDtoData: UserDto, tenant: Tenant) {
    const currentUser = await this.getCurrentUser();
    if (tenant.id !== currentUser.tenantId) {
      throw new HttpErrors.Forbidden('NotAllowedAccess');
    }

    userDtoData.email = userDtoData.email.toLowerCase().trim();
    userDtoData.username = userDtoData.username.toLowerCase().trim();

    // Check for valid email
    // sonarignore:start
    const emailRegex =
      /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/; //NOSONAR
    // sonarignore:end

    if (userDtoData.email && !emailRegex.test(userDtoData.email)) {
      throw new HttpErrors.BadRequest(`Invalid Email`);
    }
  }

  /**
   * Updates the status of a tenant user.
   * Finds the user-tenant association and updates its status.
   *
   * @param tenantId - The ID of the tenant.
   * @param userId - The ID of the user.
   * @param updateTenantUserStatusRequest - DTO containing the new status.
   * @throws 404 Not Found if the user-tenant association is not found.
   */
  async updateTenantUserStatus(
    tenantId: string,
    userId: string,
    updateTenantUserStatusRequest: UpdateTenantUserStatusRequestDto,
  ): Promise<void> {
    const user = await this.userTenantRepository.findOne({
      where: {userId: userId, tenantId},
    });

    if (!user) {
      throw new HttpErrors.NotFound(`User with id ${userId} not found`);
    }

    await this.userTenantRepository.updateById(user.id, {
      status: updateTenantUserStatusRequest.status,
    });
  }
}
