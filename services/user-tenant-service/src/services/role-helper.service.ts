import {Filter, Getter, repository} from '@loopback/repository';
import {Role, RoleRepository} from '@sourceloop/user-tenant-service';

export class RoleHelperService {
  constructor(
    @repository.getter('RoleRepository')
    protected roleRepositoryGetter: Getter<RoleRepository>,
  ) {}

  async findAllRoles(filter: Filter<Role>): Promise<Role[]> {
    const roleRepository = await this.roleRepositoryGetter();
    return roleRepository.findAll(filter);
  }
}
