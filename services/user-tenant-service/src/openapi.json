{"openapi": "3.0.0", "info": {"title": "user-tenant-service", "version": "1.0.0", "description": "user-tenant-service", "contact": {}}, "paths": {"/groups/{id}/user/bulk": {"post": {"x-controller-name": "GroupUserController", "x-operation-name": "createBulkUserGroups", "tags": ["GroupUserController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "UserGroup model instance", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserGroup"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| CreateMultipleUserGroup   |\n| 31   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NewUserGroupInGroup"}}}}, "x-parameter-index": 1}, "operationId": "GroupUserController.createBulkUserGroups"}}, "/groups/{id}/user/{userId}": {"delete": {"x-controller-name": "GroupUserController", "x-operation-name": "delete", "tags": ["GroupUserController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "DELETE Group.UserGroup"}}, "description": "\n\n| Permissions |\n| ------- |\n| RemoveMemberFromUserGroup   |\n| 22   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "userId", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "user_groups.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<UserGroup>"}}}}], "operationId": "GroupUserController.delete"}}, "/groups/{id}/user": {"post": {"x-controller-name": "GroupUserController", "x-operation-name": "create", "tags": ["GroupUserController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "UserGroup model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserGroup"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| CreateUserGroup   |\n| 1   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserGroupInGroup"}}}, "x-parameter-index": 1}, "operationId": "GroupUserController.create"}, "get": {"x-controller-name": "GroupUserController", "x-operation-name": "find", "tags": ["GroupUserController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of UserGroup of a Group", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserGroup"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewUserGroupList   |\n| 2   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true}}}}], "operationId": "GroupUserController.find"}}, "/ping": {"get": {"x-controller-name": "PingController", "x-operation-name": "ping", "tags": ["PingController"], "responses": {"200": {"description": "Ping Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PingResponse"}}}}}, "description": "", "operationId": "PingController.ping"}}, "/tenants/count": {"get": {"x-controller-name": "TenantController", "x-operation-name": "count", "tags": ["TenantController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Tenant model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewTenant   |\n| 17   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "tenants.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Tenant>"}}}}], "operationId": "TenantController.count"}}, "/tenants/{id}/groups/{groupId}": {"delete": {"x-controller-name": "TenantGroupController", "x-operation-name": "delete", "tags": ["TenantGroupController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Tenant.Group DELETE", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| DeleteGroup   |\n| 35   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "groupId", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "groups.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Group>"}}}}], "operationId": "TenantGroupController.delete"}}, "/tenants/{id}/groups": {"post": {"x-controller-name": "TenantGroupController", "x-operation-name": "create", "tags": ["TenantGroupController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Group model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Group"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| CreateGroup   |\n| 32   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewGroupInTenant"}}}, "x-parameter-index": 1}, "operationId": "TenantGroupController.create"}, "patch": {"x-controller-name": "TenantGroupController", "x-operation-name": "patch", "tags": ["TenantGroupController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Tenant.Group PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| UpdateGroup   |\n| 34   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "groups.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Group>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GroupPartial"}}}, "x-parameter-index": 1}, "operationId": "TenantGroupController.patch"}, "get": {"x-controller-name": "TenantGroupController", "x-operation-name": "find", "tags": ["TenantGroupController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Groups of Tenant", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Group"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewGroupList   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true}}}}], "operationId": "TenantGroupController.find"}}, "/tenants/{id}/role-view/count": {"get": {"x-controller-name": "RoleViewController", "x-operation-name": "count", "tags": ["RoleViewController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "TenantUserView model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 6   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "v_roles.Where<PERSON><PERSON><PERSON>", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<RoleView>"}}}}], "operationId": "RoleViewController.count"}}, "/tenants/{id}/role-view": {"get": {"x-controller-name": "RoleViewController", "x-operation-name": "find", "tags": ["RoleViewController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of TenantUserView model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RoleView"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 6   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/v_roles.Filter"}}}}], "operationId": "RoleViewController.find"}}, "/tenants/{id}/roles/all": {"get": {"x-controller-name": "RoleController", "x-operation-name": "findAllRoles", "tags": ["RoleController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Role model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Role"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 6   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/roles.Filter"}}}}], "operationId": "RoleController.findAllRoles"}}, "/tenants/{id}/roles/{roleId}": {"delete": {"x-controller-name": "TenantRoleController", "x-operation-name": "delete", "tags": ["TenantRoleController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Tenant.Role DELETE success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| DeleteRoles   |\n| 10   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "roleId", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "roles.Where<PERSON><PERSON><PERSON>", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Role>"}}}}], "operationId": "TenantRoleController.delete"}}, "/tenants/{id}/roles": {"post": {"x-controller-name": "TenantRoleController", "x-operation-name": "create", "tags": ["TenantRoleController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Role model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Role"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| CreateRoles   |\n| 8   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewRoleInTenant"}}}, "x-parameter-index": 1}, "operationId": "TenantRoleController.create"}, "patch": {"x-controller-name": "TenantRoleController", "x-operation-name": "patch", "tags": ["TenantRoleController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Tenant.Role PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| UpdateRoles   |\n| 9   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "roles.Where<PERSON><PERSON><PERSON>", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Role>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RolePartialExcluding_id-tenantId_"}}}, "x-parameter-index": 1}, "operationId": "TenantRoleController.patch"}, "get": {"x-controller-name": "TenantRoleController", "x-operation-name": "find", "tags": ["TenantRoleController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Roles of Tenant", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Role"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewRoles   |\n| 6   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true}}}}], "operationId": "TenantRoleController.find"}}, "/tenants/{id}/tenant-configs": {"post": {"x-controller-name": "TenantTenantConfigController", "x-operation-name": "create", "tags": ["TenantTenantConfigController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "TenantConfig model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantConfig"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| CreateTenant   |\n| 16   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewTenantConfigInTenant"}}}, "x-parameter-index": 1}, "operationId": "TenantTenantConfigController.create"}, "patch": {"x-controller-name": "TenantTenantConfigController", "x-operation-name": "patch", "tags": ["TenantTenantConfigController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Tenant.TenantConfig PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| UpdateTenant   |\n| 18   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "tenant_configs.WhereF<PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<TenantConfig>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewTenantConfigInTenant"}}}, "x-parameter-index": 1}, "operationId": "TenantTenantConfigController.patch"}, "get": {"x-controller-name": "TenantTenantConfigController", "x-operation-name": "find", "tags": ["TenantTenantConfigController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of TenantConfigs of Tenant", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TenantConfig"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewTenant   |\n| 17   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true}}}}], "operationId": "TenantTenantConfigController.find"}, "delete": {"x-controller-name": "TenantTenantConfigController", "x-operation-name": "delete", "tags": ["TenantTenantConfigController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Tenant.TenantConfig DELETE success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| DeleteTenant   |\n| DeleteTenantUser   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "tenant_configs.WhereF<PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<TenantConfig>"}}}}], "operationId": "TenantTenantConfigController.delete"}}, "/tenants/{id}/users/bulk": {"post": {"x-controller-name": "UserController", "x-operation-name": "createMany", "tags": ["UserController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of created User model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}}}}, "409": {"description": "Duplicate users found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "duplicateUsers": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}}}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 13   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NewUserInTenantBulk"}}}}, "x-parameter-index": 1}, "operationId": "UserController.createMany"}}, "/tenants/{id}/users/count": {"get": {"x-controller-name": "UserController", "x-operation-name": "count", "tags": ["UserController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Object containing user count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 12   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "v_users.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<UserView>"}}}}], "operationId": "UserController.count"}}, "/tenants/{tenantId}/users/{userId}/status": {"patch": {"x-controller-name": "UserController", "x-operation-name": "updateTenantUserStatus", "tags": ["UserController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "TenantUser status PATCH success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 14   |\n", "parameters": [{"name": "tenantId", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "userId", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTenantUserStatusRequestDto"}}}, "x-parameter-index": 2}, "operationId": "UserController.updateTenantUserStatus"}}, "/tenants/{id}/users/{userId}": {"patch": {"x-controller-name": "TenantUserController", "x-operation-name": "patch", "tags": ["TenantUserController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Tenant.User PATCH"}}, "description": "\n\n| Permissions |\n| ------- |\n| UpdateTenantUser   |\n| 14   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "userId", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "users.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<User>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserViewPartialExcluding_id-authClientIds-lastLogin-tenantId-tenantName-tenantKey-roleName-userTenantId_"}}}, "x-parameter-index": 2}, "operationId": "TenantUserController.patch"}, "delete": {"x-controller-name": "TenantUserController", "x-operation-name": "delete", "tags": ["TenantUserController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Tenant.User DELETE"}}, "description": "\n\n| Permissions |\n| ------- |\n| DeleteTenantUser   |\n| 15   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "userId", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "users.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<User>"}}}}], "operationId": "TenantUserController.delete"}}, "/tenants/{id}/users": {"post": {"x-controller-name": "TenantUserController", "x-operation-name": "create", "tags": ["TenantUserController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "User model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| CreateTenantUser   |\n| 13   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserInTenant"}}}, "x-parameter-index": 1}, "operationId": "TenantUserController.create"}, "get": {"x-controller-name": "TenantUserController", "x-operation-name": "find", "tags": ["TenantUserController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Users of Tenant", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewTenantUser   |\n| 12   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true}}}}], "operationId": "TenantUserController.find"}}, "/tenants/{id}": {"put": {"x-controller-name": "TenantController", "x-operation-name": "replaceById", "tags": ["TenantController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Tenant PUT success"}}, "description": "\n\n| Permissions |\n| ------- |\n| UpdateTenant   |\n| 18   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Tenant"}}}, "x-parameter-index": 1}, "operationId": "TenantController.replaceById"}, "patch": {"x-controller-name": "TenantController", "x-operation-name": "updateById", "tags": ["TenantController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Tenant PATCH success"}}, "description": "\n\n| Permissions |\n| ------- |\n| UpdateTenant   |\n| 18   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantPartial"}}}, "x-parameter-index": 1}, "operationId": "TenantController.updateById"}, "get": {"x-controller-name": "TenantController", "x-operation-name": "findById", "tags": ["TenantController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Tenant model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantWithRelations"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewTenant   |\n| 17   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/tenants.Filter"}}}}], "operationId": "TenantController.findById"}, "delete": {"x-controller-name": "TenantController", "x-operation-name": "deleteById", "tags": ["TenantController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Tenant DELETE success"}}, "description": "\n\n| Permissions |\n| ------- |\n| DeleteTenant   |\n| DeleteTenantUser   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "TenantController.deleteById"}}, "/tenants": {"post": {"x-controller-name": "TenantController", "x-operation-name": "create", "tags": ["TenantController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Tenant model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Tenant"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| CreateTenant   |\n| 16   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewTenant"}}}}, "operationId": "TenantController.create"}, "patch": {"x-controller-name": "TenantController", "x-operation-name": "updateAll", "tags": ["TenantController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Tenant PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| UpdateTenant   |\n| 18   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "tenants.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Tenant>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantPartial"}}}}, "operationId": "TenantController.updateAll"}, "get": {"x-controller-name": "TenantController", "x-operation-name": "find", "tags": ["TenantController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Tenant model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TenantWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewTenant   |\n| 17   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/tenants.Filter1"}}}}], "operationId": "TenantController.find"}}, "/user/{id}/tenants": {"get": {"x-controller-name": "UserTenantController", "x-operation-name": "find", "tags": ["UserTenantController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Tenants to Which the User Belongs", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Tenant"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewAllTenantOfSelf   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true}}}}], "operationId": "UserTenantController.find"}}, "/user-callback": {"post": {"x-controller-name": "UserWebhookController", "x-operation-name": "callback", "tags": ["UserWebhookController"], "responses": {"204": {"description": "Webhook success"}}, "description": "", "parameters": [{"name": "x-signature", "in": "header", "schema": {"type": "string"}}, {"name": "x-timestamp", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserWebhookDTO"}}}}, "operationId": "UserWebhookController.callback"}}, "/user-tenant-prefs/count": {"get": {"x-controller-name": "UserTenantPrefsController", "x-operation-name": "count", "tags": ["UserTenantPrefsController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "UserTenantPrefs model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewUserTenantPreference   |\n| 25   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "user_tenant_prefs.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<UserTenantPrefs>"}}}}], "operationId": "UserTenantPrefsController.count"}}, "/user-tenant-prefs/{id}": {"delete": {"x-controller-name": "UserTenantPrefsController", "x-operation-name": "deleteById", "tags": ["UserTenantPrefsController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "UserTenantPrefs DELETE success"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| DeleteUserTenantPreference   |\n| 30   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "UserTenantPrefsController.deleteById"}}, "/user-tenant-prefs": {"post": {"x-controller-name": "UserTenantPrefsController", "x-operation-name": "create", "tags": ["UserTenantPrefsController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "UserTenantPrefs model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserTenantPrefs"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| CreateUserTenantPreference   |\n| 29   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserTenantPrefs"}}}}, "operationId": "UserTenantPrefsController.create"}, "get": {"x-controller-name": "UserTenantPrefsController", "x-operation-name": "find", "tags": ["UserTenantPrefsController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of UserTenantPrefs model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserTenantPrefsWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewUserTenantPreference   |\n| 25   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/user_tenant_prefs.Filter"}}}}], "operationId": "UserTenantPrefsController.find"}}, "/user-tenants/{id}/user-groups": {"get": {"x-controller-name": "UserTenantUserGroupController", "x-operation-name": "find", "tags": ["UserTenantUserGroupController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of UserGroups of UserTenant", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserGroup"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewUserTenant   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true}}}}], "operationId": "UserTenantUserGroupController.find"}}, "/user-tenants/{id}/user-level-permissions": {"post": {"x-controller-name": "UserTenantUserLevelPermissionController", "x-operation-name": "create", "tags": ["UserTenantUserLevelPermissionController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "UserLevelPermission model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLevelPermission"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| CreateUserTenant   |\n| CreateUserPermissions   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserLevelPermissionInUserTenant"}}}, "x-parameter-index": 1}, "operationId": "UserTenantUserLevelPermissionController.create"}, "patch": {"x-controller-name": "UserTenantUserLevelPermissionController", "x-operation-name": "patch", "tags": ["UserTenantUserLevelPermissionController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "UserTenant.UserLevelPermission PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| UpdateUserTenant   |\n| UpdateUserPermissions   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "user_permissions.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<UserLevelPermission>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLevelPermissionPartial"}}}, "x-parameter-index": 1}, "operationId": "UserTenantUserLevelPermissionController.patch"}, "get": {"x-controller-name": "UserTenantUserLevelPermissionController", "x-operation-name": "find", "tags": ["UserTenantUserLevelPermissionController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of UserLevelPermissions of UserTenant", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserLevelPermission"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewUserTenant   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true}}}}], "operationId": "UserTenantUserLevelPermissionController.find"}, "delete": {"x-controller-name": "UserTenantUserLevelPermissionController", "x-operation-name": "delete", "tags": ["UserTenantUserLevelPermissionController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "UserTenant.UserLevelPermission DELETE success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| DeleteUserTenant   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "user_permissions.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<UserLevelPermission>"}}}}], "operationId": "UserTenantUserLevelPermissionController.delete"}}, "/": {"get": {"x-controller-name": "HomePageController", "x-operation-name": "homePage", "tags": ["HomePageController"], "responses": {"200": {"description": "Home Page", "content": {"text/html": {"schema": {"type": "string"}}}}}, "description": "", "operationId": "HomePageController.homePage"}}}, "components": {"securitySchemes": {"HTTPBearer": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"Role": {"title": "Role", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "roleType": {"type": "number", "maximum": 15, "minimum": 0}, "description": {"type": "string"}, "permissions": {"type": "array", "items": {"type": "string"}}, "allowedClients": {"type": "array", "items": {"type": "string"}}, "tenantId": {"type": "string"}, "status": {"type": "number", "minimum": 0, "maximum": 10}}, "required": ["name", "tenantId", "status"], "additionalProperties": false}, "NewRoleInTenant": {"title": "NewRoleInTenant", "type": "object", "description": "(tsType: Omit<Role, 'id' | 'tenantId' | 'status'>, schemaOptions: { title: 'NewRoleInTenant', exclude: [ 'id', 'tenantId', 'status' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "name": {"type": "string"}, "roleType": {"type": "number", "maximum": 15, "minimum": 0}, "description": {"type": "string"}, "permissions": {"type": "array", "items": {"type": "string"}}, "allowedClients": {"type": "array", "items": {"type": "string"}}}, "required": ["name"], "additionalProperties": false, "x-typescript-type": "Omit<Role, 'id' | 'tenantId' | 'status'>"}, "RolePartialExcluding_id-tenantId_": {"title": "RolePartialExcluding_id-tenantId_", "type": "object", "description": "(tsType: Omit<Partial<Role>, 'id' | 'tenantId'>, schemaOptions: { partial: true, exclude: [ 'id', 'tenantId' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "name": {"type": "string"}, "roleType": {"type": "number", "maximum": 15, "minimum": 0}, "description": {"type": "string"}, "permissions": {"type": "array", "items": {"type": "string"}}, "allowedClients": {"type": "array", "items": {"type": "string"}}, "status": {"type": "number", "minimum": 0, "maximum": 10}}, "additionalProperties": false, "x-typescript-type": "Omit<Partial<Role>, 'id' | 'tenantId'>"}, "Tenant": {"title": "Tenant", "type": "object", "description": "signature for all tenants", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "status": {"type": "number", "description": "Tenant status - Active or Inactive", "enum": [1, 0], "nullable": true}, "key": {"type": "string"}, "website": {"type": "string"}, "address": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "zip": {"type": "string"}, "country": {"type": "string"}}, "required": ["name", "status"], "additionalProperties": false}, "NewTenant": {"title": "NewTenant", "type": "object", "description": "signature for all tenants (tsType: Omit<Tenant, 'id'>, schemaOptions: { title: 'NewTenant', exclude: [ 'id' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "name": {"type": "string"}, "status": {"type": "number", "description": "Tenant status - Active or Inactive", "enum": [1, 0], "nullable": true}, "key": {"type": "string"}, "website": {"type": "string"}, "address": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "zip": {"type": "string"}, "country": {"type": "string"}}, "required": ["name", "status"], "additionalProperties": false}, "GroupWithRelations": {"title": "GroupWithRelations", "type": "object", "description": "(tsType: GroupWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "photoUrl": {"type": "string"}, "tenantId": {"type": "string"}, "userGroups": {"type": "array", "items": {"$ref": "#/components/schemas/UserGroupWithRelations"}}}, "required": ["tenantId"], "additionalProperties": false, "x-typescript-type": "GroupWithRelations"}, "TenantConfigWithRelations": {"title": "TenantConfigWithRelations", "type": "object", "description": "(tsType: TenantConfigWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "configKey": {"type": "string"}, "configValue": {"type": "object"}, "tenantId": {"type": "string"}, "tenant": {"$ref": "#/components/schemas/TenantWithRelations"}, "foreignKey": {}}, "required": ["config<PERSON><PERSON>", "tenantId"], "additionalProperties": false, "x-typescript-type": "TenantConfigWithRelations"}, "UserCredentialsWithRelations": {"title": "UserCredentialsWithRelations", "type": "object", "description": "(tsType: UserCredentialsWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "authProvider": {"type": "string"}, "authId": {"type": "string"}, "authToken": {"type": "string"}, "secretKey": {"type": "string", "description": "Secret for Authenticator app"}, "password": {"type": "string"}, "userId": {"type": "string"}, "user": {"$ref": "#/components/schemas/UserWithRelations"}, "foreignKey": {}}, "required": ["authProvider", "userId"], "additionalProperties": false, "x-typescript-type": "UserCredentialsWithRelations"}, "UserWithRelations": {"title": "UserWithRelations", "type": "object", "description": "This is signature for user model. (tsType: UserWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "middleName": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string"}, "designation": {"type": "string"}, "phone": {"type": "string", "pattern": "^\\+?[1-9]\\d{1,14}$"}, "authClientIds": {"type": "string"}, "lastLogin": {"type": "string", "format": "date-time"}, "photoUrl": {"type": "string"}, "gender": {"type": "string", "description": "This field takes a single character as input in database.\n    'M' for male and 'F' for female.", "enum": ["M", "F", "O"]}, "dob": {"type": "string", "format": "date-time"}, "defaultTenantId": {"type": "string"}, "defaultTenant": {"$ref": "#/components/schemas/TenantWithRelations"}, "foreignKey": {}, "credentials": {"$ref": "#/components/schemas/UserCredentialsWithRelations"}, "userTenants": {"type": "array", "items": {"$ref": "#/components/schemas/UserTenantWithRelations"}}}, "required": ["firstName", "username", "email"], "additionalProperties": false}, "UserViewWithRelations": {"title": "UserViewWithRelations", "type": "object", "description": "User details view in DB (tsType: UserViewWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "middleName": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string"}, "designation": {"type": "string"}, "phone": {"type": "string"}, "authClientIds": {"type": "string"}, "lastLogin": {"type": "string"}, "photoUrl": {"type": "string"}, "gender": {"type": "string", "description": "This field takes a single character as input in database.\n    'M' for male and 'F' for female.", "enum": ["M", "F", "O"]}, "dob": {"type": "string", "format": "date-time", "nullable": true}, "defaultTenantId": {"type": "string"}, "status": {"type": "number", "maximum": 11, "minimum": 0}, "tenantId": {"type": "string"}, "roleId": {"type": "string"}, "tenantName": {"type": "string"}, "tenantKey": {"type": "string"}, "roleName": {"type": "string"}, "userTenantId": {"type": "string"}}, "required": ["firstName", "username", "email", "defaultTenantId", "tenantId", "roleId", "tenantName", "userTenantId"], "additionalProperties": false}, "RoleWithRelations": {"title": "RoleWithRelations", "type": "object", "description": "(tsType: RoleWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "roleType": {"type": "number", "maximum": 15, "minimum": 0}, "description": {"type": "string"}, "permissions": {"type": "array", "items": {"type": "string"}}, "allowedClients": {"type": "array", "items": {"type": "string"}}, "tenantId": {"type": "string"}, "status": {"type": "number", "minimum": 0, "maximum": 10}, "userTenants": {"type": "array", "items": {"$ref": "#/components/schemas/UserTenantWithRelations"}}, "createdByUser": {"$ref": "#/components/schemas/UserViewWithRelations"}, "foreignKey": {}, "modifiedByUser": {"$ref": "#/components/schemas/UserViewWithRelations"}}, "required": ["name", "tenantId", "status"], "additionalProperties": false, "x-typescript-type": "RoleWithRelations"}, "UserLevelPermissionWithRelations": {"title": "UserLevelPermissionWithRelations", "type": "object", "description": "(tsType: UserLevelPermissionWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "permission": {"type": "string"}, "allowed": {"type": "boolean"}, "userTenantId": {"type": "string"}, "userTenant": {"$ref": "#/components/schemas/UserTenantWithRelations"}, "foreignKey": {}}, "required": ["permission", "allowed", "userTenantId"], "additionalProperties": false, "x-typescript-type": "UserLevelPermissionWithRelations"}, "UserGroupWithRelations": {"title": "UserGroupWithRelations", "type": "object", "description": "(tsType: UserGroupWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "groupId": {"type": "string"}, "userTenantId": {"type": "string"}, "group": {"$ref": "#/components/schemas/GroupWithRelations"}, "foreignKey": {}, "userTenant": {"$ref": "#/components/schemas/UserTenantWithRelations"}}, "required": ["groupId", "userTenantId"], "additionalProperties": false, "x-typescript-type": "UserGroupWithRelations"}, "UserInvitationWithRelations": {"title": "UserInvitationWithRelations", "type": "object", "description": "(tsType: UserInvitationWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "expiresOn": {"type": "string", "format": "date-time"}, "token": {"type": "string"}, "userTenantId": {"type": "string"}, "userTenant": {"$ref": "#/components/schemas/UserTenantWithRelations"}, "foreignKey": {}}, "required": ["userTenantId"], "additionalProperties": false, "x-typescript-type": "UserInvitationWithRelations"}, "UserTenantWithRelations": {"title": "UserTenantWithRelations", "type": "object", "description": "(tsType: UserTenantWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "locale": {"type": "string"}, "status": {"type": "number", "maximum": 12, "minimum": 0}, "userId": {"type": "string"}, "tenantId": {"type": "string"}, "roleId": {"type": "string"}, "user": {"$ref": "#/components/schemas/UserWithRelations"}, "foreignKey": {}, "tenant": {"$ref": "#/components/schemas/TenantWithRelations"}, "role": {"$ref": "#/components/schemas/RoleWithRelations"}, "userLevelPermissions": {"type": "array", "items": {"$ref": "#/components/schemas/UserLevelPermissionWithRelations"}}, "userGroups": {"type": "array", "items": {"$ref": "#/components/schemas/UserGroupWithRelations"}}, "userInvitations": {"type": "array", "items": {"$ref": "#/components/schemas/UserInvitationWithRelations"}}}, "required": ["userId", "tenantId", "roleId"], "additionalProperties": false, "x-typescript-type": "UserTenantWithRelations"}, "TenantWithRelations": {"title": "TenantWithRelations", "type": "object", "description": "signature for all tenants (tsType: TenantWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "status": {"type": "number", "description": "Tenant status - Active or Inactive", "enum": [1, 0], "nullable": true}, "key": {"type": "string"}, "website": {"type": "string"}, "address": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "zip": {"type": "string"}, "country": {"type": "string"}, "tenantConfigs": {"type": "array", "items": {"$ref": "#/components/schemas/TenantConfigWithRelations"}}, "userTenants": {"type": "array", "items": {"$ref": "#/components/schemas/UserTenantWithRelations"}}, "users": {"type": "array", "items": {"$ref": "#/components/schemas/UserWithRelations"}}, "roles": {"type": "array", "items": {"$ref": "#/components/schemas/RoleWithRelations"}}, "groups": {"type": "array", "items": {"$ref": "#/components/schemas/GroupWithRelations"}}}, "required": ["name", "status"], "additionalProperties": false}, "TenantPartial": {"title": "TenantPartial", "type": "object", "description": "signature for all tenants (tsType: Partial<Tenant>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "status": {"type": "number", "description": "Tenant status - Active or Inactive", "enum": [1, 0], "nullable": true}, "key": {"type": "string"}, "website": {"type": "string"}, "address": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "zip": {"type": "string"}, "country": {"type": "string"}}, "additionalProperties": false}, "UserGroup": {"title": "UserGroup", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "groupId": {"type": "string"}, "userTenantId": {"type": "string"}}, "required": ["groupId", "userTenantId"], "additionalProperties": false}, "NewUserGroupInGroup": {"title": "NewUserGroupInGroup", "type": "object", "description": "(tsType: Omit<UserGroup, 'id' | 'groupId'>, schemaOptions: { title: 'NewUserGroupInGroup', exclude: [ 'id', 'groupId' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "userTenantId": {"type": "string"}}, "required": ["userTenantId"], "additionalProperties": false, "x-typescript-type": "Omit<UserGroup, 'id' | 'groupId'>"}, "TenantConfig": {"title": "TenantConfig", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "configKey": {"type": "string"}, "configValue": {"type": "object"}, "tenantId": {"type": "string"}}, "required": ["config<PERSON><PERSON>", "tenantId"], "additionalProperties": false}, "NewTenantConfigInTenant": {"title": "NewTenantConfigInTenant", "type": "object", "description": "(tsType: Omit<TenantConfig, 'id' | 'tenantId'>, schemaOptions: { title: 'NewTenantConfigInTenant', exclude: [ 'id', 'tenantId' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "configKey": {"type": "string"}, "configValue": {"type": "object"}}, "required": ["config<PERSON><PERSON>"], "additionalProperties": false, "x-typescript-type": "Omit<TenantConfig, 'id' | 'tenantId'>"}, "Group": {"title": "Group", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "photoUrl": {"type": "string"}, "tenantId": {"type": "string"}}, "required": ["tenantId"], "additionalProperties": false}, "NewGroupInTenant": {"title": "NewGroupInTenant", "type": "object", "description": "(tsType: Omit<Group, 'id' | 'tenantId'>, schemaOptions: { title: 'NewGroupInTenant', exclude: [ 'id', 'tenantId' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "photoUrl": {"type": "string"}}, "additionalProperties": false, "x-typescript-type": "Omit<Group, 'id' | 'tenantId'>"}, "GroupPartial": {"title": "GroupPartial", "type": "object", "description": "(tsType: Partial<Group>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "photoUrl": {"type": "string"}, "tenantId": {"type": "string"}}, "additionalProperties": false, "x-typescript-type": "Partial<Group>"}, "User": {"title": "User", "type": "object", "description": "This is signature for user model.", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "middleName": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string"}, "designation": {"type": "string"}, "phone": {"type": "string", "pattern": "^\\+?[1-9]\\d{1,14}$"}, "authClientIds": {"type": "string"}, "lastLogin": {"type": "string", "format": "date-time"}, "photoUrl": {"type": "string"}, "gender": {"type": "string", "description": "This field takes a single character as input in database.\n    'M' for male and 'F' for female.", "enum": ["M", "F", "O"]}, "dob": {"type": "string", "format": "date-time"}, "defaultTenantId": {"type": "string"}}, "required": ["firstName", "username", "email"], "additionalProperties": false}, "NewUserInTenant": {"title": "NewUserInTenant", "type": "object", "description": "This is signature for user model. (tsType: Omit<UserDto, 'id' | 'defaultTenantId'>, schemaOptions: { title: 'NewUserInTenant', exclude: [ 'id', 'defaultTenantId' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "middleName": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string"}, "designation": {"type": "string"}, "phone": {"type": "string", "pattern": "^\\+?[1-9]\\d{1,14}$"}, "authClientIds": {"type": "string"}, "lastLogin": {"type": "string", "format": "date-time"}, "photoUrl": {"type": "string"}, "gender": {"type": "string", "description": "This field takes a single character as input in database.\n    'M' for male and 'F' for female.", "enum": ["M", "F", "O"]}, "dob": {"type": "string", "format": "date-time"}, "roleId": {"type": "string"}, "locale": {"type": "string"}}, "required": ["firstName", "username", "email", "roleId"], "additionalProperties": false}, "UserDto": {"title": "UserDto", "type": "object", "description": "This is signature for user model.", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "middleName": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string"}, "designation": {"type": "string"}, "phone": {"type": "string", "pattern": "^\\+?[1-9]\\d{1,14}$"}, "authClientIds": {"type": "string"}, "lastLogin": {"type": "string", "format": "date-time"}, "photoUrl": {"type": "string"}, "gender": {"type": "string", "description": "This field takes a single character as input in database.\n    'M' for male and 'F' for female.", "enum": ["M", "F", "O"]}, "dob": {"type": "string", "format": "date-time"}, "defaultTenantId": {"type": "string"}, "roleId": {"type": "string"}, "locale": {"type": "string"}}, "required": ["firstName", "username", "email", "roleId"], "additionalProperties": false}, "UserViewPartialExcluding_id-authClientIds-lastLogin-tenantId-tenantName-tenantKey-roleName-userTenantId_": {"title": "UserViewPartialExcluding_id-authClientIds-lastLogin-tenantId-tenantName-tenantKey-roleName-userTenantId_", "type": "object", "description": "User details view in DB (tsType: Omit<Partial<UserView>, 'id' | 'authClientIds' | 'lastLogin' | 'tenantId' | 'tenantName' | 'tenantKey' | 'roleName' | 'userTenantId'>, schemaOptions: { partial: true, exclude: [ 'id', 'authClientIds', 'lastLogin', 'tenantId', 'tenantName', 'tenantKey', 'roleName', 'userTenantId' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "middleName": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string"}, "designation": {"type": "string"}, "phone": {"type": "string"}, "photoUrl": {"type": "string"}, "gender": {"type": "string", "description": "This field takes a single character as input in database.\n    'M' for male and 'F' for female.", "enum": ["M", "F", "O"]}, "dob": {"type": "string", "format": "date-time", "nullable": true}, "defaultTenantId": {"type": "string"}, "status": {"type": "number", "maximum": 11, "minimum": 0}, "roleId": {"type": "string"}}, "additionalProperties": false}, "UserTenantPrefs": {"title": "UserTenantPrefs", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "configKey": {"type": "string"}, "configValue": {"type": "object"}, "userTenantId": {"type": "string"}}, "required": ["config<PERSON><PERSON>", "config<PERSON><PERSON><PERSON>"], "additionalProperties": false}, "NewUserTenantPrefs": {"title": "NewUserTenantPrefs", "type": "object", "description": "(tsType: Omit<UserTenantPrefs, 'id' | 'userTenantId'>, schemaOptions: { title: 'NewUserTenantPrefs', exclude: [ 'id', 'userTenantId' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "configKey": {"type": "string"}, "configValue": {"type": "object"}}, "required": ["config<PERSON><PERSON>", "config<PERSON><PERSON><PERSON>"], "additionalProperties": false, "x-typescript-type": "Omit<UserTenantPrefs, 'id' | 'userTenantId'>"}, "UserTenantPrefsWithRelations": {"title": "UserTenantPrefsWithRelations", "type": "object", "description": "(tsType: UserTenantPrefsWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "configKey": {"type": "string"}, "configValue": {"type": "object"}, "userTenantId": {"type": "string"}, "userTenant": {"$ref": "#/components/schemas/UserTenantWithRelations"}, "foreignKey": {}}, "required": ["config<PERSON><PERSON>", "config<PERSON><PERSON><PERSON>"], "additionalProperties": false, "x-typescript-type": "UserTenantPrefsWithRelations"}, "UserLevelPermission": {"title": "UserLevelPermission", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "permission": {"type": "string"}, "allowed": {"type": "boolean"}, "userTenantId": {"type": "string"}}, "required": ["permission", "allowed", "userTenantId"], "additionalProperties": false}, "NewUserLevelPermissionInUserTenant": {"title": "NewUserLevelPermissionInUserTenant", "type": "object", "description": "(tsType: @loopback/repository-json-schema#Optional<Omit<UserLevelPermission, 'id'>, 'userTenantId'>, schemaOptions: { title: 'NewUserLevelPermissionInUserTenant', exclude: [ 'id' ], optional: [ 'userTenantId' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "permission": {"type": "string"}, "allowed": {"type": "boolean"}, "userTenantId": {"type": "string"}}, "required": ["permission", "allowed"], "additionalProperties": false, "x-typescript-type": "@loopback/repository-json-schema#Optional<Omit<UserLevelPermission, 'id'>, 'userTenantId'>"}, "UserLevelPermissionPartial": {"title": "UserLevelPermissionPartial", "type": "object", "description": "(tsType: Partial<UserLevelPermission>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "permission": {"type": "string"}, "allowed": {"type": "boolean"}, "userTenantId": {"type": "string"}}, "additionalProperties": false, "x-typescript-type": "Partial<UserLevelPermission>"}, "UserWebhookDTO": {"title": "UserWebhookDTO", "type": "object", "description": "(tsType: UserWebhookDTO, schemaOptions: { title: 'UserWebhookDTO' })", "properties": {"email": {"type": "string"}, "phone": {"type": "string"}, "tenantName": {"type": "string"}, "tenantKey": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "middleName": {"type": "string"}, "cognitoAuthId": {"type": "string"}, "authClient": {"type": "object", "description": "(tsType: Omit<AuthClient, 'id'>, schemaOptions: { exclude: [ 'id' ] })", "title": "AuthClientExcluding_id_", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "clientId": {"type": "string"}, "clientSecret": {"type": "string"}, "redirectUrl": {"type": "string"}, "secret": {"type": "string"}, "accessTokenExpiration": {"type": "number"}, "refreshTokenExpiration": {"type": "number"}, "authCodeExpiration": {"type": "number"}}, "required": ["clientId", "secret", "accessTokenExpiration", "refreshTokenExpiration", "authCodeExpiration"], "additionalProperties": false, "x-typescript-type": "Omit<AuthClient, 'id'>"}, "address": {"type": "object", "description": "this model represents the address of a company (tsType: Omit<AddressDTO, 'id'>, schemaOptions: { exclude: [ 'id' ] })", "title": "AddressDTOExcluding_id_", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "address": {"type": "string", "description": "address of the company"}, "city": {"type": "string", "description": "city of the company"}, "state": {"type": "string", "description": "state of the company"}, "zip": {"type": "string", "description": "zip code of the company"}, "country": {"type": "string", "description": "country of the company"}}, "additionalProperties": false}}, "required": ["email", "tenantName", "tenantKey", "firstName"], "additionalProperties": false, "x-typescript-type": "UserWebhookDTO"}, "NewUserInTenantBulk": {"title": "NewUserInTenantBulk", "type": "object", "description": "This is signature for user model. (tsType: Omit<UserDto, 'id' | 'defaultTenantId'>, schemaOptions: { title: 'NewUserInTenantBulk', exclude: [ 'id', 'defaultTenantId' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "middleName": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string"}, "designation": {"type": "string"}, "phone": {"type": "string", "pattern": "^\\+?[1-9]\\d{1,14}$"}, "authClientIds": {"type": "string"}, "lastLogin": {"type": "string", "format": "date-time"}, "photoUrl": {"type": "string"}, "gender": {"type": "string", "description": "This field takes a single character as input in database.\n    'M' for male and 'F' for female.", "enum": ["M", "F", "O"]}, "dob": {"type": "string", "format": "date-time"}, "roleId": {"type": "string"}, "locale": {"type": "string"}}, "required": ["firstName", "username", "email", "roleId"], "additionalProperties": false}, "UpdateTenantUserStatusRequestDto": {"title": "UpdateTenantUserStatusRequestDto", "type": "object", "description": "DTO for updating the status of a tenant user. UserStatus mapping: 0=REGISTERED, 1=ACTIVE, 2=INACTIVE (tsType: UpdateTenantUserStatusRequestDto, schemaOptions: { title: 'UpdateTenantUserStatusRequestDto' })", "properties": {"status": {"type": "number", "enum": [0, 1, 2], "description": "UserStatus: 0=REGISTERED, 1=ACTIVE, 2=INACTIVE", "errorMessage": {"enum": "Status must be a valid UserStatus integer value: 0=REGISTERED, 1=ACTIVE, 2=INACTIVE"}}}, "required": ["status"], "additionalProperties": false}, "RoleView": {"title": "Role<PERSON>iew", "type": "object", "description": "User details view in DB", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "roleId": {"type": "string"}, "roleName": {"type": "string"}, "status": {"type": "number"}, "tenantId": {"type": "string"}, "userCount": {"type": "number"}}, "required": ["roleId", "tenantId", "userCount"], "additionalProperties": false}, "PingResponse": {"type": "object", "title": "PingResponse", "properties": {"greeting": {"type": "string"}, "date": {"type": "string"}, "url": {"type": "string"}, "headers": {"type": "object", "properties": {"Content-Type": {"type": "string"}}, "additionalProperties": true}}}, "loopback.Count": {"type": "object", "title": "loopback.Count", "x-typescript-type": "@loopback/repository#Count", "properties": {"count": {"type": "number"}}}, "v_roles.Filter": {"type": "object", "title": "v_roles.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "v_roles.Where<PERSON><PERSON><PERSON>", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "roleId": {"type": "boolean"}, "roleName": {"type": "boolean"}, "status": {"type": "boolean"}, "tenantId": {"type": "boolean"}, "userCount": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "roleId", "<PERSON><PERSON><PERSON>", "status", "tenantId", "userCount"], "example": "deleted"}, "uniqueItems": true}], "title": "v_roles.<PERSON>"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<RoleView>"}, "roles.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "roles.<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "roles.IncludeFilter.Items": {"title": "roles.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["userTenants", "createdByUser", "modifiedByUser"]}, "scope": {"$ref": "#/components/schemas/roles.ScopeFilter"}}}, "roles.Filter": {"type": "object", "title": "roles.<PERSON><PERSON>", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "roles.Where<PERSON><PERSON><PERSON>", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "name": {"type": "boolean"}, "roleType": {"type": "boolean"}, "description": {"type": "boolean"}, "permissions": {"type": "boolean"}, "allowedClients": {"type": "boolean"}, "tenantId": {"type": "boolean"}, "status": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "name", "roleType", "description", "permissions", "allowedClients", "tenantId", "status"], "example": "deleted"}, "uniqueItems": true}], "title": "roles.<PERSON>"}, "include": {"title": "roles.<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/roles.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Role>"}, "tenants.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "tenants.ScopeFilter"}, "tenants.IncludeFilter.Items": {"title": "tenants.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["tenantConfigs", "userTenants", "users", "roles", "groups"]}, "scope": {"$ref": "#/components/schemas/tenants.ScopeFilter"}}}, "tenants.Filter": {"type": "object", "title": "tenants.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "name": {"type": "boolean"}, "status": {"type": "boolean"}, "key": {"type": "boolean"}, "website": {"type": "boolean"}, "address": {"type": "boolean"}, "city": {"type": "boolean"}, "state": {"type": "boolean"}, "zip": {"type": "boolean"}, "country": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "name", "status", "key", "website", "address", "city", "state", "zip", "country"], "example": "deleted"}, "uniqueItems": true}], "title": "tenants.Fields"}, "include": {"title": "tenants.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/tenants.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Tenant>"}, "tenants.Filter1": {"type": "object", "title": "tenants.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "tenants.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "name": {"type": "boolean"}, "status": {"type": "boolean"}, "key": {"type": "boolean"}, "website": {"type": "boolean"}, "address": {"type": "boolean"}, "city": {"type": "boolean"}, "state": {"type": "boolean"}, "zip": {"type": "boolean"}, "country": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "name", "status", "key", "website", "address", "city", "state", "zip", "country"], "example": "deleted"}, "uniqueItems": true}], "title": "tenants.Fields"}, "include": {"title": "tenants.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/tenants.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Tenant>"}, "user_tenant_prefs.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "user_tenant_prefs.ScopeFilter"}, "user_tenant_prefs.IncludeFilter.Items": {"title": "user_tenant_prefs.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["userTenant"]}, "scope": {"$ref": "#/components/schemas/user_tenant_prefs.ScopeFilter"}}}, "user_tenant_prefs.Filter": {"type": "object", "title": "user_tenant_prefs.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "user_tenant_prefs.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "configKey": {"type": "boolean"}, "configValue": {"type": "boolean"}, "userTenantId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "config<PERSON><PERSON>", "config<PERSON><PERSON><PERSON>", "userTenantId"], "example": "deleted"}, "uniqueItems": true}], "title": "user_tenant_prefs.Fields"}, "include": {"title": "user_tenant_prefs.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/user_tenant_prefs.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<UserTenantPrefs>"}}}, "servers": [{"url": "/"}]}