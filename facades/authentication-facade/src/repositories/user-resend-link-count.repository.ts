import {inject} from '@loopback/core';
import {DefaultKeyValueRepository, Entity, juggler} from '@loopback/repository';
import {AuthCacheSourceName} from '@sourceloop/authentication-service';
import {UserResendLinkCount} from '../models/user-resend-link-count.model';

/**
 * Repository for managing UserResendLinkCount entities in the key-value data source.
 * Handles storage and retrieval of resend link count information for users.
 */
export class UserResendLinkCountRepository<
  T extends UserResendLinkCount = UserResendLinkCount,
> extends DefaultKeyValueRepository<T> {
  /**
   * Creates an instance of UserResendLinkCountRepository.
   * @param dataSource The injected key-value data source for caching.
   * @param userResendLinkCount The UserResendLinkCount model constructor.
   */
  constructor(
    @inject(`datasources.${AuthCacheSourceName}`)
    dataSource: juggler.DataSource,

    @inject('models.UserResendLinkCount')
    private readonly userResendLinkCount: typeof Entity & {prototype: T},
  ) {
    super(userResendLinkCount, dataSource);
  }
}
