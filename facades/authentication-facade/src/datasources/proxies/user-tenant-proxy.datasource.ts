import {inject, lifeCycleObserver, LifeCycleObserver} from '@loopback/core';
import {juggler} from '@loopback/repository';
import {CONTENT_TYPE} from '@sourceloop/core';

const baseUrl = '/tenants/{id}/users';
const tokenKey = '{token}';
const tenantRoleBaseUrl = '/tenants/{id}/roles';
const bearerTokenKey = 'Bearer {token}';

/**
 * Configuration object for UserTenantService REST datasource.
 * Defines connection details, endpoints, headers, and supported operations.
 */
const config = {
  name: 'UserTenantService',
  connector: 'rest',
  baseURL: '',
  crud: false,
  options: {
    baseUrl: process.env.USER_TENANT_SERVICE_URL as string,
    headers: {
      accept: CONTENT_TYPE.JSON,
      ['content-type']: CONTENT_TYPE.JSON,
    },
  },
  operations: [
    {
      template: {
        method: 'GET',
        url: baseUrl,
        headers: {
          Authorization: 'Bearer ' + tokenKey,
        },
        query: {
          filter: '{filter}',
        },
      },
      functions: {
        find: ['token', 'id', 'filter'],
      },
    },
    {
      template: {
        method: 'GET',
        url: '/tenants/{tenantId}/role-view',
        headers: {
          Authorization: tokenKey,
        },
        query: {
          filter: '{filter}',
        },
      },
      functions: {
        getRoleView: ['token', 'tenantId', 'filter'],
      },
    },
    {
      template: {
        method: 'GET',
        url: '/tenants/{tenantId}/role-view/count',
        query: {
          where: '{where}',
        },
        headers: {
          Authorization: tokenKey,
        },
      },
      functions: {
        getRoleViewCount: ['token', 'tenantId', 'where'],
      },
    },
    {
      template: {
        method: 'GET',
        url: baseUrl,
        headers: {
          Authorization: 'Bearer ' + tokenKey,
        },
        query: {
          filter: '{filter}',
        },
      },
      functions: {
        getUsersList: ['token', 'id', 'filter'],
      },
    },
    {
      template: {
        method: 'get',
        url: baseUrl + '/count',
        headers: {
          Authorization: tokenKey,
        },
        query: {
          where: '{where}',
        },
      },
      functions: {
        getUserCount: ['token', 'id', 'where'],
      },
    },
    {
      template: {
        method: 'PATCH',
        url: baseUrl + '/{userId}',
        headers: {
          Authorization: tokenKey,
        },
        body: '{body}',
      },
      functions: {
        updateUser: ['token', 'id', 'userId', 'body'],
      },
    },
    {
      template: {
        method: 'POST',
        url: baseUrl + '/bulk',
        headers: {
          Authorization: tokenKey,
        },
        body: '{body}',
      },
      functions: {
        createBulkUsers: ['token', 'id', 'body'],
      },
    },
    {
      template: {
        method: 'PATCH',
        url: baseUrl + '/{userId}/status',
        headers: {
          Authorization: tokenKey,
        },
        body: '{body}',
      },
      functions: {
        updateUserStatus: ['token', 'id', 'userId', 'body'],
      },
    },
    {
      template: {
        method: 'GET',
        url: tenantRoleBaseUrl,
        headers: {
          Authorization: bearerTokenKey,
        },
        query: {
          filter: '{filter}',
        },
      },
      functions: {
        getRoles: ['token', 'id', 'filter'],
      },
    },
    {
      template: {
        method: 'POST',
        url: tenantRoleBaseUrl,
        headers: {
          Authorization: 'Bearer ' + tokenKey,
        },
        body: '{body}',
      },
      functions: {
        addRole: ['token', 'id', 'body'],
      },
    },
    {
      template: {
        method: 'GET',
        url: tenantRoleBaseUrl + '/all',
        headers: {
          Authorization: bearerTokenKey,
        },
        query: {
          filter: '{filter}',
        },
      },
      functions: {
        getAllRoles: ['token', 'id', 'filter'],
      },
    },
    {
      template: {
        method: 'PATCH',
        url: tenantRoleBaseUrl,
        headers: {
          Authorization: bearerTokenKey,
        },
        body: '{body}',
        query: {
          where: '{where}',
        },
      },
      functions: {
        updateRole: ['token', 'id', 'body', 'where'],
      },
    },
  ],
};

// Observe application's life cycle to disconnect the datasource when
// application is stopped. This allows the application to be shut down
// gracefully. The `stop()` method is inherited from `juggler.DataSource`.
// Learn more at https://loopback.io/doc/en/lb4/Life-cycle.html
@lifeCycleObserver('datasource')
export class UserTenantServiceDataSource
  extends juggler.DataSource
  implements LifeCycleObserver
{
  static readonly dataSourceName = 'UserTenantService';
  static readonly defaultConfig = config;

  constructor(
    @inject('datasources.config.UserTenantService', {optional: true})
    dsConfig: object = config,
  ) {
    super(dsConfig);
  }
}
