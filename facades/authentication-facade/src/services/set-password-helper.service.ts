import {
  PermissionKey,
  UpdatePasswordDto as ServiceUpdatePasswordDto,
} from '@local/core';
import {BindingScope, inject, injectable} from '@loopback/context';
import {service} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors, Request, RestBindings} from '@loopback/rest';
import {AuthenticationBindings} from '@sourceloop/authentication-service';
import {IAuthUserWithPermissions} from '@sourceloop/core';
import {UpdatePasswordDto} from '../models/dto';
import {UserTokenRepository} from '../repositories/user-token.repository';
import {CryptoHelperService} from './crypto-helper.service';
import {AuthenticationProxyService} from './proxies/authentication-proxy.provider';

/**
 * Service that handles the forget-password and password reset functionality.
 *
 * Provides methods to securely update a user's password, including token generation,
 * proxying to the authentication service, and cleanup of temporary tokens.
 */
@injectable({scope: BindingScope.TRANSIENT})
export class SetPasswordHelperService {
  /**
   * Constructs a new SetPasswordHelperService.
   *
   * @param authProxyService - Service to handle user authentication operations.
   * @param cryptoHelperService - Utility service for token generation and encryption.
   * @param request - The current HTTP request object.
   * @param currentUser - The currently authenticated user.
   * @param userTokenRepository - Repository for managing user tokens.
   */
  constructor(
    @inject(`services.AuthenticationProxyService`)
    private readonly authProxyService: AuthenticationProxyService,

    @service(CryptoHelperService)
    private readonly cryptoHelperService: CryptoHelperService,

    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,

    @inject(AuthenticationBindings.CURRENT_USER)
    private readonly currentUser: IAuthUserWithPermissions,

    @repository(UserTokenRepository)
    public userTokenRepository: UserTokenRepository,
  ) {}

  /**
   * Updates the user's password by calling the authentication proxy,
   * and deletes the temporary token after successful reset.
   *
   * @param dto - DTO containing the new password.
   * @throws {HttpErrors.InternalServerError} If client credentials are missing in environment variables.
   * @throws {HttpErrors.BadRequest} If the authorization header is missing.
   * @returns {Promise<void>} Resolves when the password is updated and the token is deleted.
   */
  async setPassword(dto: UpdatePasswordDto): Promise<void> {
    // Retrieve client credentials from environment variables
    const clientId = process.env.CLIENT_ID;
    const clientSecret = process.env.CLIENT_SECRET;

    if (!clientId || !clientSecret) {
      throw new HttpErrors.InternalServerError(
        'CLIENT_ID or CLIENT_SECRET is not defined in environment variables',
      );
    }

    // Generate a temporary token with the required permissions for password update
    const token = this.cryptoHelperService.generateTempToken({
      id: this.currentUser.id,
      userTenantId: this.currentUser.userTenantId,
      tenantId: this.currentUser.tenantId,
      permissions: [PermissionKey.UpdatePassword],
    });

    // Update the password through the authentication proxy service
    await this.authProxyService.setPassword(
      token,
      new ServiceUpdatePasswordDto({
        newPassword: dto.newPassword,
        clientId: clientId,
        clientSecret: clientSecret,
      }),
    );

    // Extract the Bearer token from the Authorization header for cleanup
    const authorizationHeader = this.request.headers.authorization?.replace(
      /^Bearer\s+/i,
      '',
    );

    if (!authorizationHeader) {
      throw new HttpErrors.BadRequest('Authorization header is missing');
    }

    // Delete the used token from the repository to prevent reuse
    await this.userTokenRepository.delete(authorizationHeader);
  }
}
