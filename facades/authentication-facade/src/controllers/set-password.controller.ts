﻿// Copyright (c) 2023 Sourcefuse Technologies
//
// This software is released under the MIT License.
// https://opensource.org/licenses/MIT

import {inject} from '@loopback/core';
import {getModelSchemaRef, post, requestBody} from '@loopback/rest';
import {
  CONTENT_TYPE,
  <PERSON>rror<PERSON><PERSON>,
  OPERATION_SECURITY_SPEC,
  STATUS_CODE,
} from '@sourceloop/core';
import {STRATEGY, authenticate} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {FORGET_PASSWORD_TOKEN_VERIFIER} from '../key';
import {UpdatePasswordDto} from '../models/dto';
import {SetPasswordHelperService} from '../services/set-password-helper.service';

/**
 * Controller for handling forget and update password related endpoints.
 *
 * Provides an endpoint for users to set a new password after verifying a token,
 * typically used in the forget password flow.
 */
export class SetPasswordController {
  /**
   * Creates a new instance of the SetPasswordController.
   *
   * @param setPasswordHelperService - Service to handle set password logic.
   */
  constructor(
    @inject('services.SetPasswordHelperService')
    private readonly setPasswordHelperService: SetPasswordHelperService,
  ) {}

  /**
   * Endpoint to update the password after verifying the token.
   *
   * @param dto - Data Transfer Object containing new password and token.
   * @returns A void promise indicating completion of the password update.
   *
   * @remarks
   * This endpoint is protected using Bearer token authentication and a custom token verifier.
   *
   * @endpoint POST /auth/set-password/verify
   * @status 204 No Content on success
   * @security Bearer token authentication with forget password token verifier.
   */
  @authorize({permissions: ['*']})
  @authenticate(
    STRATEGY.BEARER,
    {
      passReqToCallback: true,
    },
    undefined,
    FORGET_PASSWORD_TOKEN_VERIFIER,
  )
  @post(`auth/set-password/verify`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Success Response.',
      },
      ...ErrorCodes,
    },
  })
  async setPassword(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(UpdatePasswordDto),
        },
      },
    })
    dto: UpdatePasswordDto,
  ): Promise<void> {
    // Delegate password update logic to the helper service
    await this.setPasswordHelperService.setPassword(dto);
  }
}
