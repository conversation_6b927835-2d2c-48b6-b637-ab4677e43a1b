import {inject} from '@loopback/context';
import {
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  requestBody,
  RestBindings,
} from '@loopback/rest';
import {Filter} from '@loopback/repository';
import {
  CONTENT_TYPE,
  OPERATION_SECURITY_SPEC,
  STATUS_CODE,
} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {PermissionKey} from '@local/core';
import {authorize} from 'loopback4-authorization';
import {RoleHelperService} from '../services';
import {Role} from '@sourceloop/user-tenant-service';
import {UpdateRoleDto} from '../models/dto/update-role.dto.model';
const baseUrl = '/roles';

/**
 * Controller for handling role-related endpoints.
 * Provides APIs to fetch roles.
 */
export class RoleController {
  /**
   * Constructs a new RoleController.
   * @param roleHelperService - Service for role-related logic.
   * @param request - The current HTTP request object.
   */
  constructor(
    @inject('services.RoleHelperService')
    private readonly roleHelperService: RoleHelperService,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
  ) {}

  /**
   * Retrieves a list of roles.
   * Requires bearer authentication and appropriate permissions.
   * @param filter - Optional filter criteria for querying roles.
   * @returns A promise resolving to an array of Role objects.
   */
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @authorize({
    permissions: [PermissionKey.ViewRoles],
  })
  @get(baseUrl, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Roles',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Role)},
          },
        },
      },
    },
  })
  async find(
    @param.query.object('filter') filter?: Filter<Role>,
  ): Promise<Role[]> {
    // Delegate the role retrieval to the roleHelperService
    return this.roleHelperService.getRoles(filter);
  }

  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @authorize({
    permissions: [PermissionKey.AddRole],
  })
  @post(baseUrl, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Role Object',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Role, {
              exclude: ['id', 'tenantId', 'userTenants', 'status'],
            }),
          },
        },
      },
    },
  })
  async add(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Role, {
            exclude: ['id', 'tenantId', 'userTenants', 'status'],
          }),
        },
      },
    })
    dto: Role,
  ): Promise<Role> {
    // Delegate the role retrieval to the roleHelperService
    return this.roleHelperService.addRole(dto);
  }

  /**
   * Retrieves a list of roles.
   * Requires bearer authentication and appropriate permissions.
   * @param filter - Optional filter criteria for querying roles.
   * @returns A promise resolving to an array of Role objects.
   */
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @authorize({
    permissions: [PermissionKey.ViewRoles],
  })
  @get(baseUrl + '/all', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Roles',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Role)},
          },
        },
      },
    },
  })
  async findAll(
    @param.query.object('filter') filter?: Filter<Role>,
  ): Promise<Role[]> {
    // Delegate the role retrieval to the roleHelperService
    return this.roleHelperService.getAllRoles(filter);
  }

  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @authorize({
    permissions: [PermissionKey.UpdateRole],
  })
  @patch(baseUrl + '/{id}', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Roles object',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Role),
          },
        },
      },
    },
  })
  async updateRole(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Role, {
            partial: true,
            exclude: ['id', 'tenantId', 'userTenants'],
          }),
        },
      },
    })
    dto: Partial<UpdateRoleDto>,
    @param.path.string('id') id: string,
  ): Promise<Role> {
    // Delegate the role retrieval to the roleHelperService
    return this.roleHelperService.updateRole(id, dto);
  }

  /**
   * Retrieves all role statuses.
   * Requires bearer authentication and appropriate permissions.
   * @returns A promise resolving to an array of role statuses.
   */

  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @authorize({
    permissions: [PermissionKey.ViewRoles],
  })
  @get(baseUrl + '/all-statuses', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Role Statuses',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  key: {type: 'string'},
                  value: {type: 'string'},
                },
              },
            },
          },
        },
      },
    },
  })
  async getAllRoleStatuses(): Promise<Record<string, string>> {
    // Delegate the role status retrieval to the roleHelperService
    return this.roleHelperService.getAllRoleStatuses();
  }
}
