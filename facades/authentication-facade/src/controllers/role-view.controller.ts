import {inject} from '@loopback/core';
import {
  get,
  RestBindings,
  Request,
  getModelSchemaRef,
  param,
} from '@loopback/rest';
import {
  CONTENT_TYPE,
  ErrorCodes,
  OPERATION_SECURITY_SPEC,
  STATUS_CODE,
} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {UserTenantServiceHelper} from '../services';
import {MergeFilterBuilder, PermissionKey, RoleView} from '@local/core';
import {Count, Filter, Where, WhereBuilder} from '@loopback/repository';
// Ensure TenantUserView is a class, not just a type. If it's only a type, change its definition to a class or interface.

const basePath = '/roles';

/**
 * Controller for managing tenant role operations.
 *
 */
export class RoleViewController {
  /**
   * The authorization token extracted from the request headers.
   * This token is used to authenticate the user for various operations.
   *
   */

  token: string;
  constructor(
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @inject('services.UserTenantServiceHelper')
    private readonly usertenantService: UserTenantServiceHelper,
  ) {
    const token = this.request.headers['authorization'];
    if (!token) {
      throw new Error('Authorization token is missing');
    }
    this.token = token;
  }

  /**
   * Get the roles for the authenticated user.
   * @returns An array of roles for the user.
   */

  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @authorize({permissions: [PermissionKey.ViewRoles]})
  @get(`${basePath}/role-view`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      permission: [PermissionKey.ViewRoles],
      [STATUS_CODE.OK]: {
        description: 'Get Roles',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              items: getModelSchemaRef(RoleView),
              type: 'array',
            },
          },
        },
      },
      ...ErrorCodes,
    },
  })
  async getRoles(
    @param.filter(RoleView) filter?: Filter<RoleView>,
  ): Promise<RoleView[]> {
    const refinedFilter = new MergeFilterBuilder<RoleView>(filter);
    refinedFilter.mergeWhere({
      roleId: {
        neq: process.env.SUPER_ADMIN_ROLE_ID,
      },
    });
    return this.usertenantService.getRoles(this.token, refinedFilter.build());
  }

  /**
   * Get the roles for the authenticated user.
   * @returns An array of roles for the user.
   */

  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @authorize({permissions: [PermissionKey.ViewRoles]})
  @get(`${basePath}/role-view/count`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      permission: [PermissionKey.ViewRoles],
      [STATUS_CODE.OK]: {
        description: 'Get Roles view count',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'object',
              properties: {
                count: {type: 'number'},
              },
            },
          },
        },
      },
      ...ErrorCodes,
    },
  })
  async getRolesCount(
    @param.where(RoleView) where?: Where<RoleView>,
  ): Promise<Count> {
    const whereCondition = new WhereBuilder(where);

    whereCondition.impose({
      ...where,
      roleId: {
        neq: process.env.SUPER_ADMIN_ROLE_ID,
      },
    });

    return this.usertenantService.getRolesCount(
      this.token,
      whereCondition.build(),
    );
  }
}
