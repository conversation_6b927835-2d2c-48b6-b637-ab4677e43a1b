import {Per<PERSON><PERSON>ey} from '@local/core';
import {inject} from '@loopback/context';
import {Count, Filter, Where} from '@loopback/repository';
import {
  get,
  getModelSchemaRef,
  param,
  RestBindings,
  Request,
  requestBody,
  patch,
  HttpErrors,
  post,
} from '@loopback/rest';
import {
  CONTENT_TYPE,
  OPERATION_SECURITY_SPEC,
  STATUS_CODE,
} from '@sourceloop/core';
import {Role, User, UserView} from '@sourceloop/user-tenant-service';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {
  RoleHelperService,
  UserHelperService,
  UserTenantProxyService,
} from '../services';
import {CreateUserRequestDto} from '../models/dto';
import {UpdateUserStatusRequestDto} from '../models/dto/update-user-status-req.dto.model';

const baseUrl = '/users';

/**
 * Controller for handling user-related endpoints.
 * Provides APIs to fetch, update, and manage users.
 */
export class UserController {
  /**
   * Constructs a new UserController.
   * @param userOperationsService - Service for user operations via proxy.
   * @param userHelperService - Helper service for user-related logic.
   * @param roleHelperService - Helper service for role-related logic.
   * @param request - The current HTTP request object.
   */
  constructor(
    @inject('services.UserTenantProxyService')
    private readonly userOperationsService: UserTenantProxyService,
    @inject('services.UserHelperService')
    private readonly userHelperService: UserHelperService,
    @inject('services.RoleHelperService')
    private readonly roleHelperService: RoleHelperService,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
  ) {}

  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @authorize({
    permissions: [PermissionKey.ViewTenantUser],
  })
  @get(baseUrl, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Users',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(User)},
          },
        },
      },
    },
  })

  /**
   * Retrieves a list of users.
   * Requires bearer authentication and appropriate permissions.
   * @param filter - Optional filter criteria for querying users.
   * @returns A promise resolving to an array of UserView objects.
   */
  async find(
    @param.query.object('filter') filter?: Filter<UserView>,
  ): Promise<UserView[]> {
    // Extract the bearer token from the Authorization header
    // Extract the bearer token from the Authorization header
    const token = (this.request.headers.authorization ?? '')
      .trim()
      .replace(/^Bearer\s+/i, '')
      .trim();
    // Delegate the user list retrieval to the userOperationsService
    return this.userOperationsService.find(
      token,
      process.env.DEFAULT_TENANT_ID ?? '',
      filter,
    );
  }

  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @authorize({
    permissions: [PermissionKey.ViewTenantUser],
  })
  @get(`${baseUrl}/user-list`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Users',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(User)},
          },
        },
      },
    },
  })

  /**
   * Retrieves a refined list of users, excluding super admin users.
   * Requires bearer authentication and appropriate permissions.
   * @param filter - Optional filter criteria for querying users.
   * @returns A promise resolving to an array of UserView objects.
   */
  async getUserLists(
    @param.filter(UserView) filter?: Filter<UserView>,
  ): Promise<UserView[]> {
    // Delegate the user list retrieval to the userHelperService
    return this.userHelperService.getUserLists(filter);
  }

  @authorize({
    permissions: [PermissionKey.ViewTenantUser],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${baseUrl}/count`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'count of Users',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                count: {type: 'number'},
              },
            },
          },
        },
      },
    },
  })

  /**
   * Retrieves the count of users, excluding super admin users.
   * Requires bearer authentication and appropriate permissions.
   * @param where - Optional filter criteria for users.
   * @returns A promise resolving to the count of users.
   */
  async getUserCount(
    @param.where(UserView)
    where?: Where<UserView>,
  ): Promise<Count> {
    // Delegate the user count retrieval to the userHelperService
    return this.userHelperService.getUsersCount(where);
  }

  @authorize({
    permissions: [PermissionKey.UpdateTenantUser],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @patch(`${baseUrl}/{id}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Update the Users',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'object',
              items: getModelSchemaRef(UserView, {partial: true}),
            },
          },
        },
      },
    },
  })
  /**
   * Updates a user's details by their unique identifier.
   * Requires bearer authentication and appropriate permissions.
   * @param id - The unique identifier of the user.
   * @param dto - The update request DTO containing new user details.
   * @returns The updated user view object.
   */
  async updateUser(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(CreateUserRequestDto, {
            title: 'Update User',
            partial: true, // make all fields optional
            exclude: ['email'], // disallow email updates
          }),
        },
      },
    })
    dto: Partial<Omit<CreateUserRequestDto, 'email'>>,
  ): Promise<UserView> {
    // Delegate the update operation to the userHelperService
    return this.userHelperService.updateUser(id, dto);
  }

  @authorize({
    permissions: [PermissionKey.ViewTenantUser, PermissionKey.ViewRoles],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${baseUrl}/{id}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Get User by ID',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: getModelSchemaRef(UserView, {partial: true}),
          },
        },
      },
      [STATUS_CODE.NOT_FOUND]: {
        description: 'User not found',
      },
    },
  })
  /**
   * Retrieves a user by their unique identifier, along with their role if available.
   * Requires bearer authentication and appropriate permissions.
   * @param id - The unique identifier of the user.
   * @returns An object containing the user and their role (if found).
   * @throws {HttpErrors.NotFound} If user is not found.
   */
  async getUserById(
    @param.path.string('id') id: string,
  ): Promise<{user: UserView; role: Role | undefined}> {
    // Retrieve user by id using the helper service
    const user = await this.userHelperService.getUserById(id);

    if (!user) {
      throw new HttpErrors.NotFound(`User with id ${id} not found`);
    }

    let role: Role | undefined = undefined;
    if (user.roleId) {
      // Retrieve the user's role if roleId is present
      const roles = await this.roleHelperService.getRoles({
        where: {id: user.roleId},
      });
      role = roles.length > 0 ? roles[0] : undefined;
    }

    return {user, role};
  }

  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @authorize({
    permissions: [PermissionKey.UpdateTenantUser],
  })
  @patch(`${baseUrl}/{id}/status`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Update user status',
      },
    },
  })
  /**
   * Updates the status of a user by their unique identifier.
   * Requires bearer authentication and appropriate permissions.
   * @param userId - The unique identifier of the user.
   * @param body - DTO containing the new status.
   */
  async updateUserStatus(
    @param.path.string('id') userId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UpdateUserStatusRequestDto, {
            title: 'UpdateUserStatusRequestDto',
          }),
        },
      },
    })
    body: UpdateUserStatusRequestDto,
  ): Promise<void> {
    // Delegate the status update to the userHelperService
    await this.userHelperService.updateUserStatus(userId, body);
  }

  @authorize({
    permissions: [PermissionKey.CreateTenantUser],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @post(`${baseUrl}/bulk`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Bulk create Users',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(UserView, {partial: true}),
            },
          },
        },
      },
      [STATUS_CODE.CONFLICT]: {
        description: 'Duplicate users found',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'object',
              properties: {
                error: {type: 'string'},
                duplicateUsers: {type: 'array', items: {type: 'string'}},
              },
            },
          },
        },
      },
    },
  })
  /**
   * Creates multiple users in bulk.
   * Requires bearer authentication and appropriate permissions.
   * @param dtos - Array of user creation request DTOs.
   * @returns Array of created users or error with duplicate users.
   */
  async createUsersBulk(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'array',
            items: getModelSchemaRef(CreateUserRequestDto, {
              title: 'Create bulk new users',
            }),
          },
        },
      },
    })
    dtos: CreateUserRequestDto[],
  ): Promise<User[] | {error: string; duplicateUsers: string[]}> {
    // Delegate the bulk user creation to the userHelperService
    return this.userHelperService.createBulkUsers(dtos);
  }

  @authorize({
    permissions: [PermissionKey.UpdateTenantUser],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @post(`${baseUrl}/{id}/resend-invitation`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Resends invitation email to the user',
      },
    },
  })
  /**
   * Resends the invitation email to a user.
   *
   * @param id - The ID of the user to resend the invitation to.
   * @returns A void promise indicating completion.
   *
   * @endpoint POST /tenants/users/{id}/resend-invitation
   * @security Bearer authentication required.
   * @response 204 - Invitation email resent.
   */
  async resendInvitationEmail(
    @param.path.string('id') id: string,
  ): Promise<void> {
    return this.userHelperService.resendInvitationEmail(id);
  }

  /**
   *
   * @returns return statuses for user view
   */

  @authorize({
    permissions: [PermissionKey.ViewTenantUser],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${baseUrl}/all-statuses`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of User Statuses',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  key: {type: 'string'},
                  value: {type: 'string'},
                },
              },
            },
          },
        },
      },
    },
  })
  async getAllUserViewStatuses(): Promise<Record<string, string>> {
    // Delegate the user status retrieval to the userHelperService
    return this.userHelperService.getAllUserViewStatuses();
  }
}
