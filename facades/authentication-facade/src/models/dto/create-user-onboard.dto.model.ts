import {model, Model, property} from '@loopback/repository';

/**
 * DTO for onboarding a new user.
 *
 * @remarks
 * Used to capture details required for onboarding a user
 */
@model({
  description: 'DTO for adding a user',
})
export class CreateUserOnboardDto extends Model {
  /**
   * The first name of the user.
   */
  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      minLength: 7,
      maxLength: 100,
      pattern: '^[A-Za-z ]+$',
      errorMessage: {
        minLength: 'Full Name should have at least 7 characters',
        maxLength: 'Full Name should have at most 100 characters',
        pattern: 'Full Name should only contain letters and spaces',
      },
    },
  })
  firstName: string;

  /**
   * The email address of the user.
   */
  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      minLength: 5,
      maxLength: 254,
      format: 'email',
      errorMessage: {
        minLength: 'Email Address should have at least 5 characters',
        maxLength: 'Email Address should have at most 254 characters',
        format: 'Email Address is not in a valid format',
      },
    },
  })
  email: string;

  /**
   * The username for the user.
   */
  @property({
    type: 'string',
    required: true,
  })
  username: string;

  /**
   * The role ID assigned to the user.
   */
  @property({
    type: 'string',
    required: true,
  })
  roleId: string;

  /**
   * Constructor to initialize CreateUserOnboardDto.
   *
   * @param data - Optional partial data to populate the DTO.
   */
  constructor(data?: Partial<CreateUserOnboardDto>) {
    super(data);
  }
}
