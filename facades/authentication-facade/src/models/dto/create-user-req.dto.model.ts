import {model, Model, property} from '@loopback/repository';

/**
 * DTO for creating a new user.
 *
 * @remarks
 * Used to capture details required for creating a user.
 */
@model({
  description: 'DTO for adding a user',
})
export class CreateUserRequestDto extends Model {
  /**
   * The full name of the user.
   */
  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      minLength: 7,
      maxLength: 100,
      pattern: '^[A-Za-z ]+$',
      errorMessage: {
        minLength: 'Full Name should have at least 7 characters',
        maxLength: 'Full Name should have at most 100 characters',
        pattern: 'Full Name should only contain letters and spaces',
      },
    },
  })
  fullName: string;

  /**
   * The email address of the user.
   */
  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      minLength: 5,
      maxLength: 254,
      format: 'email',
      errorMessage: {
        minLength: 'Email Address should have at least 5 characters',
        maxLength: 'Email Address should have at most 254 characters',
        format: 'Email Address is not in a valid format',
      },
    },
  })
  email: string;

  /**
   * The role ID assigned to the user.
   */
  @property({
    type: 'string',
    required: true,
  })
  roleId: string;

  /**
   * Constructor to initialize CreateUserRequestDto.
   *
   * @param data - Optional partial data to populate the DTO.
   */
  constructor(data?: Partial<CreateUserRequestDto>) {
    super(data);
  }
}
