import {model, Model, property} from '@loopback/repository';

/**
 * DTO for updating a user's password during the forget password flow.
 *
 * Used as the payload for password reset endpoints. Contains the new password to be set.
 */
@model({
  description:
    'DTO describing the payload used to update the password during the forget password flow for a tenant.',
})
export class UpdatePasswordDto extends Model {
  /**
   * The new password to be set for the user.
   * Must meet password policy requirements.
   *
   * @example "StrongP@ssw0rd!"
   */
  @property({type: 'string', required: true, name: 'new_password'})
  newPassword: string;

  /**
   * Creates an instance of UpdatePasswordDto.
   * @param data - Optional partial data to populate the DTO.
   */
  constructor(data?: Partial<UpdatePasswordDto>) {
    super(data);
  }
}
