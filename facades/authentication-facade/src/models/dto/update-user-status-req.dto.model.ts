import {model, Model, property} from '@loopback/repository';

/**
 * DTO for updating the status of a user.
 *
 * Used to change the user's status.
 *
 * UserStatus mapping:
 * - 1 = ACTIVE
 * - 2 = INACTIVE
 */
@model({
  description:
    'DTO for updating the status of a user. UserStatus mapping: 1=ACTIVE, 2=INACTIVE',
})
export class UpdateUserStatusRequestDto extends Model {
  /**
   * The new status for the user.
   * 1 = ACTIVE, 2 = INACTIVE
   */
  @property({
    type: 'number',
    required: true,
    jsonSchema: {
      enum: [1, 2],
      description: 'UserStatus: 1=ACTIVE, 2=INACTIVE',
      errorMessage: {
        enum: 'Status must be a valid UserStatus integer value: 1=ACTIVE, 2=INACTIVE',
      },
    },
  })
  status!: number;

  /**
   * Creates an instance of UpdateUserStatusRequestDto.
   * @param data - Optional partial data to populate the DTO.
   */
  constructor(data?: Partial<UpdateUserStatusRequestDto>) {
    super(data);
  }
}
