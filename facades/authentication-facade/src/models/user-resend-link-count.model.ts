import {Entity, model, property} from '@loopback/repository';

/**
 * Model representing the resend link count for a user.
 * Used to track how many times a user has requested a resend of a specific token (e.g., verification or reset link).
 */
@model()
export class UserResendLinkCount extends Entity {
  /**
   * The count of resend link requests for a user.
   */
  @property({
    type: 'number',
    required: true,
  })
  count: number;

  /**
   * Creates an instance of UserResendLinkCount.
   * @param data Partial data to initialize the model.
   */
  constructor(data?: Partial<UserResendLinkCount>) {
    super(data);
  }
}
