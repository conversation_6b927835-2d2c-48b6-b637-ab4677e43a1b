import {expect} from '@loopback/testlab';
import sinon from 'sinon';
import {RoleViewController} from '../../../controllers/role-view.controller';
import {UserTenantServiceHelper} from '../../../services';
import {Filter, Count, Where} from '@loopback/repository';
import {RoleView} from '@local/core';
import {Request} from '@loopback/rest';

describe('RoleViewController (unit)', () => {
  let controller: RoleViewController;
  let userTenantService: sinon.SinonStubbedInstance<UserTenantServiceHelper>;
  const mockToken = 'Bearer dummy-token';
  const mockRequest = {
    headers: {
      authorization: mockToken,
    },
  } as Request;

  beforeEach(() => {
    userTenantService = sinon.createStubInstance(UserTenantServiceHelper);
    controller = new RoleViewController(
      mockRequest,
      userTenantService as unknown as UserTenantServiceHelper,
    );
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('constructor', () => {
    it('should throw error when authorization token is missing', () => {
      const requestWithoutAuth = {
        headers: {},
      } as Request;

      expect(() => {
        new RoleViewController(
          requestWithoutAuth,
          userTenantService as unknown as UserTenantServiceHelper,
        );
      }).to.throw('Authorization token is missing');
    });
  });

  describe('getRoles', () => {
    it('should return roles when called with filter', async () => {
      const mockFilter: Filter<RoleView> = {
        where: {
          tenantId: 'mocked-tenant-id',
        },
      };

      const mockRoles = [
        {
          roleId: '1',
          tenantId: 'mocked-tenant-id',
        },
      ] as RoleView[];

      userTenantService.getRoles.resolves(mockRoles);

      const result = await controller.getRoles(mockFilter);

      expect(result).to.deepEqual(mockRoles);
      // The controller merges the filter with roleId neq condition
      sinon.assert.calledOnce(userTenantService.getRoles);
      const [token, filter] = userTenantService.getRoles.getCall(0).args;
      expect(token).to.equal(mockToken);
      expect(filter).to.not.be.undefined();
      expect(typeof filter).to.equal('object');
      if (filter?.where) {
        expect(typeof filter.where).to.equal('object');
        // Check that the filter has been modified to include the roleId condition
        expect(JSON.stringify(filter)).to.containEql('roleId');
      }
    });

    it('should return roles when called without filter', async () => {
      const mockRoles = [
        {
          roleId: '1',
          tenantId: 'mocked-tenant-id',
        },
      ] as RoleView[];

      userTenantService.getRoles.resolves(mockRoles);

      const result = await controller.getRoles();

      expect(result).to.deepEqual(mockRoles);
      // The controller creates a filter with roleId neq condition
      sinon.assert.calledOnce(userTenantService.getRoles);
      const [token, filter] = userTenantService.getRoles.getCall(0).args;
      expect(token).to.equal(mockToken);
      expect(typeof filter).to.equal('object');
      if (filter?.where) {
        expect(typeof filter.where).to.equal('object');
        // Check that the filter has been modified to include the roleId condition
        expect(JSON.stringify(filter)).to.containEql('roleId');
      }
    });
  });

  describe('getRolesCount', () => {
    it('should return count when called with where clause', async () => {
      const mockWhere: Where<RoleView> = {
        tenantId: 'mocked-tenant-id',
      };

      const mockCount: Count = {
        count: 5,
      };

      userTenantService.getRolesCount.resolves(mockCount);

      const result = await controller.getRolesCount(mockWhere);

      expect(result).to.deepEqual(mockCount);
      // The controller merges the where clause with roleId neq condition
      sinon.assert.calledOnce(userTenantService.getRolesCount);
      const [token, where] = userTenantService.getRolesCount.getCall(0).args;
      expect(token).to.equal(mockToken);
      expect(typeof where).to.equal('object');
      // Check that the where clause has been modified to include the roleId condition
      expect(JSON.stringify(where)).to.containEql('roleId');
      expect(JSON.stringify(where)).to.containEql('mocked-tenant-id');
    });

    it('should return count when called without where clause', async () => {
      const mockCount: Count = {
        count: 3,
      };

      userTenantService.getRolesCount.resolves(mockCount);

      const result = await controller.getRolesCount();

      expect(result).to.deepEqual(mockCount);
      // The controller creates a where clause with roleId neq condition
      sinon.assert.calledOnce(userTenantService.getRolesCount);
      const [token, where] = userTenantService.getRolesCount.getCall(0).args;
      expect(token).to.equal(mockToken);
      expect(typeof where).to.equal('object');
      // Check that the where clause has been modified to include the roleId condition
      expect(JSON.stringify(where)).to.containEql('roleId');
    });
  });
});
