// Unit tests for SetPasswordHelperService

import {expect} from '@loopback/testlab';
import sinon, {SinonStub} from 'sinon';
import {SetPasswordHelperService} from '../../services/set-password-helper.service';
import {CryptoHelperService} from '../../services/crypto-helper.service';
import {AuthenticationProxyService} from '../../services/proxies/authentication-proxy.provider';
import {UserTokenRepository} from '../../repositories/user-token.repository';
import {Request} from '@loopback/rest';
import {UpdatePasswordDto} from '../../models/dto';

describe('SetPasswordHelperService', () => {
  let service: SetPasswordHelperService;
  let authProxyServiceStub: AuthenticationProxyService;
  let cryptoHelperStub: CryptoHelperService;
  let userTokenRepositoryStub: UserTokenRepository;
  let mockRequest: Partial<Request>;
  let currentUser: {
    id: string;
    userTenantId: string;
    tenantId: string;
    permissions: string[];
    authClientId: number;
    role: string;
    firstName: string;
    lastName: string;
    username: string;
  };

  beforeEach(() => {
    authProxyServiceStub = {
      setPassword: sinon.stub().resolves(),
    } as unknown as AuthenticationProxyService;

    cryptoHelperStub = {
      generateTempToken: sinon.stub().returns('mocked-token'),
    } as unknown as CryptoHelperService;

    userTokenRepositoryStub = {
      delete: sinon.stub().resolves(),
    } as unknown as UserTokenRepository;

    mockRequest = {
      headers: {authorization: 'Bearer some-token'},
    };

    currentUser = {
      id: 'user-id',
      userTenantId: 'tenant-user-id',
      tenantId: 'tenant-id',
      permissions: [],
      authClientId: 123,
      role: 'user',
      firstName: 'Test',
      lastName: 'User',
      username: 'testuser',
    };

    process.env.CLIENT_ID = 'client-id';
    process.env.CLIENT_SECRET = 'client-secret';

    service = new SetPasswordHelperService(
      authProxyServiceStub,
      cryptoHelperStub,
      mockRequest as Request,
      currentUser,
      userTokenRepositoryStub,
    );
  });

  afterEach(() => {
    sinon.restore();
  });

  it('should update password and delete token', async () => {
    const dto = new UpdatePasswordDto({newPassword: 'newpass'});
    await service.setPassword(dto);

    sinon.assert.calledWith(
      authProxyServiceStub.setPassword as SinonStub,
      'mocked-token',
      sinon.match({
        newPassword: 'newpass',
        clientId: 'client-id',
        clientSecret: 'client-secret',
      }),
    );
    sinon.assert.calledWith(
      userTokenRepositoryStub.delete as SinonStub,
      'some-token',
    );
  });

  it('should throw InternalServerError if CLIENT_ID or CLIENT_SECRET is missing', async () => {
    delete process.env.CLIENT_ID;
    const dto = new UpdatePasswordDto({newPassword: 'newpass'});
    await expect(service.setPassword(dto)).to.be.rejectedWith(
      'CLIENT_ID or CLIENT_SECRET is not defined in environment variables',
    );
  });

  it('should throw BadRequest if Authorization header is missing', async () => {
    service = new SetPasswordHelperService(
      authProxyServiceStub,
      cryptoHelperStub,
      {headers: {}} as Request,
      currentUser,
      userTokenRepositoryStub,
    );
    const dto = new UpdatePasswordDto({newPassword: 'newpass'});
    await expect(service.setPassword(dto)).to.be.rejectedWith(
      'Authorization header is missing',
    );
  });
});
