import {Request} from '@loopback/rest';
import {expect, sinon} from '@loopback/testlab';
import {
  CodeResponse,
  RefreshTokenRequest,
  TokenResponse,
} from '@sourceloop/authentication-service';
import {AuthTokenRequest, LoginRequest} from '../../models';
import {LoginHelperService} from '../../services/login-helper.service';
import {AuthenticationProxyService} from '../../services/proxies/authentication-proxy.provider';
import {mockUser} from '../acceptance/test-helper';

describe('LoginHelperService (unit)', () => {
  let loginHelper: LoginHelperService;
  let authProxyServiceStub: AuthenticationProxyService;
  let mockRequest: Partial<Request>;

  beforeEach(() => {
    mockRequest = {
      headers: {},
    };

    authProxyServiceStub = {
      login: sinon.stub(),
      getToken: sinon.stub(),
      me: sinon.stub(),
      logout: sinon.stub(),
      forgetPassword: sinon.stub(),
      updatePassword: sinon.stub(),
      refreshToken: sinon.stub(),
      setPassword: sinon.stub(),
    };

    loginHelper = new LoginHelperService(
      authProxyServiceStub,
      mockRequest as Request,
    );
  });

  describe('me()', () => {
    it('returns user info when token is valid', async () => {
      if (!mockRequest.headers) {
        mockRequest.headers = {};
      }
      mockRequest.headers.authorization = 'Bearer test.token';

      (authProxyServiceStub.me as sinon.SinonStub).resolves(mockUser);

      const result = await loginHelper.me();
      expect(result).to.deepEqual(mockUser);
      sinon.assert.calledWith(
        authProxyServiceStub.me as sinon.SinonStub,
        'test.token',
      );
    });

    it('throws Unauthorized if no token is provided', async () => {
      await expect(loginHelper.me()).to.be.rejectedWith('Unauthorized');
    });
  });

  describe('login()', () => {
    const envBackup = {...process.env};

    before(() => {
      process.env.CLIENT_ID = 'test-client-id';
      process.env.CLIENT_SECRET = 'test-client-secret';
    });

    after(() => {
      process.env = envBackup;
    });

    it('calls login and returns token', async () => {
      const loginRequest: LoginRequest = new LoginRequest({
        username: 'user',
        password: 'pass',
      });

      const codeRes: CodeResponse = {
        code: 'auth-code-xyz',
      };

      const tokenRes: TokenResponse = new TokenResponse({
        accessToken: 'access-token-xyz',
        refreshToken: 'refresh-token-xyz',
      });

      (authProxyServiceStub.login as sinon.SinonStub).resolves(codeRes);
      (authProxyServiceStub.getToken as sinon.SinonStub).resolves(tokenRes);

      const result = await loginHelper.login(loginRequest);

      expect(result).to.deepEqual(tokenRes);

      sinon.assert.calledWithMatch(
        authProxyServiceStub.login as sinon.SinonStub,
        /* eslint-disable @typescript-eslint/naming-convention */
        {
          username: 'user',
          password: 'pass',
          //
          client_id: 'test-client-id',
          client_secret: 'test-client-secret',
        },
      );

      sinon.assert.calledWithMatch(
        authProxyServiceStub.getToken as sinon.SinonStub,
        {
          code: 'auth-code-xyz',
          clientId: 'test-client-id',
        },
      );
    });
  });

  describe('getToken()', () => {
    before(() => {
      process.env.CLIENT_ID = 'client-id-xyz';
    });

    it('calls getToken and returns response', async () => {
      const req: AuthTokenRequest = new AuthTokenRequest({
        code: 'code-123',
      });

      const tokenRes: TokenResponse = new TokenResponse({
        accessToken: 'access-token-abc',
        refreshToken: 'refresh-token-abc',
      });

      (authProxyServiceStub.getToken as sinon.SinonStub).resolves(tokenRes);

      const result = await loginHelper.getToken(req);
      expect(result).to.deepEqual(tokenRes);

      sinon.assert.calledWithMatch(
        authProxyServiceStub.getToken as sinon.SinonStub,
        {
          code: 'code-123',
          clientId: 'client-id-xyz',
        },
      );
    });
  });

  describe('logout()', () => {
    it('calls logout and returns result', async () => {
      const token = 'access-token';
      const refreshReq: RefreshTokenRequest = new RefreshTokenRequest({
        refreshToken: 'refresh-token-abc',
      });
      const logoutResponse = {success: true};

      (authProxyServiceStub.logout as sinon.SinonStub).resolves(logoutResponse);

      const result = await loginHelper.logout(token, refreshReq);
      expect(result).to.deepEqual(logoutResponse);

      sinon.assert.calledWith(
        authProxyServiceStub.logout as sinon.SinonStub,
        token,
        refreshReq,
      );
    });
  });
});
