// Unit tests for RoleHelperService
import {expect, sinon} from '@loopback/testlab';
import {RestBindings, HttpErrors, Request} from '@loopback/rest';
import {Context} from '@loopback/context';
import {Role} from '@sourceloop/user-tenant-service';
import {RoleHelperService} from '../../../services/role-helper.service';
import {RoleStatus} from '@local/core';

const userProxyServiceStub = {
  getRoles: sinon.stub(),
  find: sinon.stub(),
  getRoleView: sinon.stub(),
  getRoleViewCount: sinon.stub(),
  getUsersList: sinon.stub(),
  createBulkUsers: sinon.stub(),
  updateUser: sinon.stub(),
  updateUserStatus: sinon.stub(),
  getUserCount: sinon.stub(),
  addRole: sinon.stub(),
  getAllRoles: sinon.stub(),
  updateRole: sinon.stub(),
};

describe('RoleHelperService', () => {
  let request: Request;
  let ctx: Context;

  beforeEach(() => {
    Object.values(userProxyServiceStub).forEach(
      stub => stub.reset && stub.reset(),
    );
    ctx = new Context();
    ctx.bind('services.UserTenantProxyService').to(userProxyServiceStub);
  });

  it('throws Unauthorized if authorization header missing', () => {
    request = {headers: {}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);
    expect(() => {
      new RoleHelperService(request, userProxyServiceStub);
    }).to.throw(HttpErrors.Unauthorized);
  });

  it('getRoles throws InternalServerError if DEFAULT_TENANT_ID missing', async () => {
    request = {headers: {authorization: 'Bearer token123'}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);

    const service = new RoleHelperService(request, userProxyServiceStub);
    delete process.env.DEFAULT_TENANT_ID;
    await expect(service.getRoles()).to.be.rejectedWith(
      HttpErrors.InternalServerError,
    );
  });

  it('getRoles returns roles excluding super admin', async () => {
    process.env.DEFAULT_TENANT_ID = 'tenant1';
    process.env.SUPER_ADMIN_ROLE_NAME = 'superadmin';
    request = {headers: {authorization: 'Bearer token123'}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);

    const roles = [
      new Role({id: '1', name: 'user'}),
      new Role({id: '2', name: 'admin'}),
    ];
    userProxyServiceStub.getRoles.resolves(roles);

    const service = new RoleHelperService(request, userProxyServiceStub);
    const result = await service.getRoles();
    expect(result).to.eql(roles);
    sinon.assert.calledOnce(userProxyServiceStub.getRoles);
    const [token, tenantId, filter] =
      userProxyServiceStub.getRoles.getCall(0).args;
    expect(token).to.equal('token123');
    expect(tenantId).to.equal('tenant1');
    // Check the filter structure - it should have an 'and' array with name and status conditions
    expect(Array.isArray(filter.where.and)).to.be.true();
    expect(filter.where.and).to.containEql({name: {neq: 'superadmin'}});
  });

  it('getRoles applies additional filter', async () => {
    process.env.DEFAULT_TENANT_ID = 'tenant2';
    process.env.SUPER_ADMIN_ROLE_NAME = 'superadmin';
    request = {headers: {authorization: 'Bearer token456'}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);

    const roles = [new Role({id: '3', name: 'manager'})];
    userProxyServiceStub.getRoles.resolves(roles);

    const service = new RoleHelperService(request, userProxyServiceStub);
    const filter = {where: {custom: 'value'}} as {
      where: {[key: string]: unknown};
    };
    const result = await service.getRoles(filter);
    expect(result).to.eql(roles);
    sinon.assert.calledOnce(userProxyServiceStub.getRoles);
    const [token, tenantId, calledFilter] =
      userProxyServiceStub.getRoles.getCall(0).args;
    expect(token).to.equal('token456');
    expect(tenantId).to.equal('tenant2');
    // Check the filter structure - it should have an 'and' array with the original filter, name and status conditions
    expect(Array.isArray(calledFilter.where.and)).to.be.true();
    expect(calledFilter.where.and).to.containEql({custom: 'value'});
    expect(calledFilter.where.and).to.containEql({name: {neq: 'superadmin'}});
  });
});

describe('getAllRoleStatuses', () => {
  let service: RoleHelperService;
  // Use the stub from the outer scope
  beforeEach(() => {
    const request = {headers: {authorization: 'Bearer token'}} as Request;
    service = new RoleHelperService(request, userProxyServiceStub);
  });

  it('should return correct statuses', async () => {
    const result = await service.getAllRoleStatuses();
    expect(result).to.eql({
      [RoleStatus.ACTIVE]: 'Active',
      [RoleStatus.INACTIVE]: 'Inactive',
    });
  });

  it('should resolve to an object with correct keys', async () => {
    const result = await service.getAllRoleStatuses();
    expect(Object.keys(result)).to.containEql(RoleStatus.ACTIVE.toString());
    expect(Object.keys(result)).to.containEql(RoleStatus.INACTIVE.toString());
  });

  it('should resolve to an object with correct values', async () => {
    const result = await service.getAllRoleStatuses();
    expect(Object.values(result)).to.containEql('Active');
    expect(Object.values(result)).to.containEql('Inactive');
  });
});
