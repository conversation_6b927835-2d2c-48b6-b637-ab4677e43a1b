import {expect} from '@loopback/testlab';
import sinon from 'sinon';
import {
  UserTenantProxyServiceProvider,
  IUserView,
} from '../../../../services/proxies/user-tenant-proxy.provider';
import {
  CreateUserOnboardDto,
  CreateUserRequestDto,
} from '../../../../models/dto';
import {UpdateUserStatusRequestDto} from '../../../../models/dto/update-user-status-req.dto.model';
import {RoleView} from '@local/core';
import {Role} from '@sourceloop/user-tenant-service';
import {UserTenantServiceDataSource} from '../../../../datasources/proxies/user-tenant-proxy.datasource';
import {Filter, Count} from '@loopback/repository';
import {User} from '@sourceloop/authentication-service';

describe('UserTenantProxyServiceProvider (unit)', () => {
  let provider: UserTenantProxyServiceProvider;
  let mockService: {
    find: sinon.SinonStub<
      [string, string, Filter<IUserView> | string | undefined],
      Promise<IUserView[]>
    >;
    getRoleView: sinon.SinonStub<
      [string, string, string?],
      Promise<RoleView[]>
    >;
    getRoleViewCount: sinon.SinonStub<
      [string, string, string?],
      Promise<{count: number}>
    >;
    getUserCount: sinon.SinonStub<
      [string, string, string?],
      Promise<{count: number}>
    >;
    getUsersList: sinon.SinonStub<
      [string, string, Filter<IUserView> | string | undefined],
      Promise<IUserView[]>
    >;
    updateUser: sinon.SinonStub<
      [string, string, string, Partial<Omit<CreateUserRequestDto, 'email'>>],
      Promise<User>
    >;
    createBulkUsers: sinon.SinonStub<
      [string, string, CreateUserOnboardDto[]],
      Promise<User[]>
    >;
    updateUserStatus: sinon.SinonStub<
      [string, string, string, UpdateUserStatusRequestDto],
      Promise<User>
    >;
    getRoles: sinon.SinonStub<
      [string, string, Filter<Role> | string],
      Promise<Role[]>
    >;
  };

  beforeEach(() => {
    mockService = {
      find: sinon
        .stub<
          [string, string, Filter<IUserView> | string | undefined],
          Promise<IUserView[]>
        >()
        .resolves([]),
      getRoleView: sinon
        .stub<[string, string, string?], Promise<RoleView[]>>()
        .resolves([]),
      getRoleViewCount: sinon
        .stub<[string, string, string?], Promise<{count: number}>>()
        .resolves({count: 0}),
      getUserCount: sinon
        .stub<[string, string, string?], Promise<{count: number}>>()
        .resolves({count: 0}),
      getUsersList: sinon
        .stub<
          [string, string, Filter<IUserView> | string | undefined],
          Promise<IUserView[]>
        >()
        .resolves([]),
      updateUser: sinon
        .stub<
          [
            string,
            string,
            string,
            Partial<Omit<CreateUserRequestDto, 'email'>>,
          ],
          Promise<User>
        >()
        .resolves({} as User),
      createBulkUsers: sinon
        .stub<[string, string, CreateUserOnboardDto[]], Promise<User[]>>()
        .resolves([]),
      updateUserStatus: sinon
        .stub<
          [string, string, string, UpdateUserStatusRequestDto],
          Promise<User>
        >()
        .resolves({} as User),
      getRoles: sinon
        .stub<[string, string, Filter<Role> | string], Promise<Role[]>>()
        .resolves([]),
    };

    const mockDataSource = {
      name: 'UserTenantService',
      connector: 'rest',
      settings: {
        baseURL: 'http://example.com',
        crud: false,
      },
      dataSource: 'UserTenantService',
    } as unknown as UserTenantServiceDataSource;

    // Add async value method to provider that returns our mock service
    provider = new UserTenantProxyServiceProvider(mockDataSource);
    sinon
      .stub(provider, 'value')
      .resolves(
        mockService as unknown as Awaited<
          ReturnType<UserTenantProxyServiceProvider['value']>
        >,
      );
  });

  afterEach(() => {
    sinon.restore();
  });

  it('should be instantiated with datasource', () => {
    expect(provider).to.be.instanceOf(UserTenantProxyServiceProvider);
  });

  describe('service methods', () => {
    const mockToken = 'dummy-token';
    const mockTenantId = 'dummy-tenant-id';

    beforeEach(async () => {
      await provider.value();
    });

    it('should call find method with correct parameters', async () => {
      const mockFilter: Filter<IUserView> = {
        where: {id: 'dummy-id'},
      };
      const expectedUsers = [
        {
          id: 'dummy-id',
          email: '<EMAIL>',
          firstName: 'Test',
          username: 'testuser',
          authClientIds: '',
          defaultTenantId: 'tenant-1',
          tenantId: 'tenant-1',
          roleId: 'role-1',
          tenantName: 'Tenant 1',
          userTenantId: 'ut-1',
        },
      ] as IUserView[];

      mockService.find.resolves(expectedUsers);

      const result = await mockService.find(
        mockToken,
        mockTenantId,
        JSON.stringify(mockFilter),
      );

      expect(result).to.eql(expectedUsers);
      sinon.assert.calledWith(
        mockService.find,
        mockToken,
        mockTenantId,
        JSON.stringify(mockFilter),
      );
    });

    it('should call getRoleView method with correct parameters', async () => {
      const mockFilter = {
        where: {roleId: 'dummy-role'},
      };
      const expectedRoles = [
        {roleId: 'dummy-role', tenantId: mockTenantId},
      ] as RoleView[];
      mockService.getRoleView.resolves(expectedRoles);

      const result = await mockService.getRoleView(
        mockToken,
        mockTenantId,
        JSON.stringify(mockFilter),
      );

      expect(result).to.eql(expectedRoles);
      sinon.assert.calledWith(
        mockService.getRoleView,
        mockToken,
        mockTenantId,
        JSON.stringify(mockFilter),
      );
    });

    it('should call getRoleViewCount method with correct parameters', async () => {
      const mockWhere = {
        roleId: 'dummy-role',
      };
      const expectedCount: Count = {
        count: 5,
      };

      mockService.getRoleViewCount.resolves(expectedCount);

      const result = await mockService.getRoleViewCount(
        mockToken,
        mockTenantId,
        JSON.stringify(mockWhere),
      );

      expect(result).to.eql(expectedCount);
      sinon.assert.calledWith(
        mockService.getRoleViewCount,
        mockToken,
        mockTenantId,
        JSON.stringify(mockWhere),
      );
    });
  });
});
