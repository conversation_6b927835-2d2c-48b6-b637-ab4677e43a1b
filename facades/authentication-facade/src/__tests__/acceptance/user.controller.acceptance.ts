import {Client, expect, sinon} from '@loopback/testlab';
import {getToken, setupApplication} from './test-helper';
import {AuthenticationFacadeApplication} from '../../application';
import {UserTenantProxyService} from '../../services';
import {Role, User, UserView} from '@sourceloop/user-tenant-service';
import {PermissionKey} from '@local/core';

describe('UserController Acceptance', () => {
  let app: AuthenticationFacadeApplication;
  let client: Client;
  let userTenantProxyServiceStub: Partial<UserTenantProxyService>;

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());

    // Stub service
    userTenantProxyServiceStub = {
      find: sinon.stub().resolves([
        {
          id: 'user-1',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
        } as unknown as UserView,
      ]),
    };

    // Bind stub to app
    app.bind('services.UserTenantProxyService').to(userTenantProxyServiceStub);
  });

  after(async () => {
    await app.stop();
  });

  it('GET /users - should call UserTenantProxyService.find and return users', async () => {
    const fakeToken = getToken([PermissionKey.ViewTenantUser], false);
    const fakeTenantId = process.env.DEFAULT_TENANT_ID ?? '';

    const res = await client
      .get('/users')
      .set('Authorization', fakeToken)
      .expect(200);

    expect(res.body).to.deepEqual([
      {
        id: 'user-1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
      },
    ]);

    sinon.assert.calledOnce(userTenantProxyServiceStub.find as sinon.SinonStub);

    const [passedToken, passedTenantId, passedFilter] = (
      userTenantProxyServiceStub.find as sinon.SinonStub
    ).getCall(0).args;

    // Ensure Bearer is stripped
    expect(passedToken).to.equal(fakeToken.replace(/^bearer\s+/i, ''));
    expect(passedTenantId).to.equal(fakeTenantId);
    expect(passedFilter).to.equal(undefined);
  });

  it('GET /users with filter - should pass filter to service', async () => {
    const fakeToken = getToken([PermissionKey.ViewTenantUser], false);
    const filter = {where: {firstName: 'John'}};

    await client
      .get('/users')
      .query({filter: JSON.stringify(filter)})
      .set('Authorization', fakeToken)
      .expect(200);

    const [, , passedFilter] = (
      userTenantProxyServiceStub.find as sinon.SinonStub
    ).getCall(1).args;

    expect(passedFilter).to.deepEqual(filter);
  });
});

// Acceptance tests for /users/userLists (getUserLists)
describe('UserController getUserLists Acceptance', () => {
  let app: AuthenticationFacadeApplication;
  let client: Client;
  let userHelperServiceStub: {getUserLists: sinon.SinonStub};

  before('setupApplication', async () => {
    const setup = await setupApplication();
    app = setup.app;
    client = setup.client;

    userHelperServiceStub = {
      getUserLists: sinon.stub().resolves([
        {
          id: 'user-2',
          firstName: 'Alice',
          lastName: 'Smith',
          email: '<EMAIL>',
        },
      ]),
    };

    app.bind('services.UserHelperService').to(userHelperServiceStub);
  });

  after(async () => {
    await app.stop();
  });

  it('GET /users/userLists returns user list from helper', async () => {
    const fakeToken = getToken([PermissionKey.ViewTenantUser], false);
    const res = await client
      .get('/users/user-list')
      .set('Authorization', fakeToken)
      .expect(200);

    expect(res.body).to.deepEqual([
      {
        id: 'user-2',
        firstName: 'Alice',
        lastName: 'Smith',
        email: '<EMAIL>',
      },
    ]);
    sinon.assert.calledOnce(userHelperServiceStub.getUserLists);
    const [passedFilter] = userHelperServiceStub.getUserLists.getCall(0).args;
    expect(passedFilter).to.equal(undefined);
  });

  it('GET /users/userLists with filter passes filter to helper', async () => {
    const fakeToken = getToken([PermissionKey.ViewTenantUser], false);
    const filter = {where: {firstName: 'Alice'}};
    await client
      .get('/users/user-list')
      .query({filter: JSON.stringify(filter)})
      .set('Authorization', fakeToken)
      .expect(200);

    const [passedFilter] = userHelperServiceStub.getUserLists.getCall(1).args;
    expect(passedFilter).to.deepEqual(filter);
  });
});

// Acceptance tests for /users/count (getUserCount)
describe('UserController getUserCount Acceptance', () => {
  let app: AuthenticationFacadeApplication;
  let client: Client;
  let userHelperServiceStub: {getUsersCount: sinon.SinonStub};

  before('setupApplication', async () => {
    const setup = await setupApplication();
    app = setup.app;
    client = setup.client;

    userHelperServiceStub = {
      getUsersCount: sinon.stub().resolves({count: 5}),
    };

    app.bind('services.UserHelperService').to(userHelperServiceStub);
  });

  after(async () => {
    await app.stop();
  });

  it('GET /users/count returns user count from helper', async () => {
    const fakeToken = getToken([PermissionKey.ViewTenantUser], false);
    const res = await client
      .get('/users/count')
      .set('Authorization', fakeToken)
      .expect(200);

    expect(res.body).to.deepEqual({count: 5});
    sinon.assert.calledOnce(userHelperServiceStub.getUsersCount);
    const [passedWhere] = userHelperServiceStub.getUsersCount.getCall(0).args;
    expect(passedWhere).to.equal(undefined);
  });

  it('GET /users/count with where passes where to helper', async () => {
    const fakeToken = getToken([PermissionKey.ViewTenantUser], false);
    const where = {firstName: 'Alice'};
    await client
      .get('/users/count')
      .query({where: JSON.stringify(where)})
      .set('Authorization', fakeToken)
      .expect(200);

    const [passedWhere] = userHelperServiceStub.getUsersCount.getCall(1).args;
    expect(passedWhere).to.deepEqual(where);
  });
});

describe('UserController PATCH /users/{id} (updateUser)', () => {
  let app: AuthenticationFacadeApplication;
  let client: Client;
  let userHelperServiceStub: {updateUser: sinon.SinonStub};

  before('setupApplication', async () => {
    const setup = await setupApplication();
    app = setup.app;
    client = setup.client;

    userHelperServiceStub = {
      updateUser: sinon
        .stub()
        .resolves(new UserView({id: 'user-1', firstName: 'Updated'})),
    };
    app.bind('services.UserHelperService').to(userHelperServiceStub);
  });

  after(async () => {
    await app.stop();
  });

  it('PATCH /users/{id} updates user and returns updated user', async () => {
    const fakeToken = getToken([PermissionKey.UpdateTenantUser], false);
    const res = await client
      .patch('/users/user-1')
      .set('Authorization', fakeToken)
      .send({fullName: 'Updated', roleId: 'roleX'})
      .expect(200);

    expect(res.body).to.deepEqual({id: 'user-1', firstName: 'Updated'});
    sinon.assert.calledOnce(userHelperServiceStub.updateUser);
    const [id, dto] = userHelperServiceStub.updateUser.getCall(0).args;
    expect(id).to.equal('user-1');
    expect(dto).to.containDeep({fullName: 'Updated', roleId: 'roleX'});
  });
});

describe('UserController GET /users/{id} (getUserById)', () => {
  let app: AuthenticationFacadeApplication;
  let client: Client;
  let userHelperServiceStub: {getUserById: sinon.SinonStub};
  let roleHelperServiceStub: {getRoles: sinon.SinonStub};

  before('setupApplication', async () => {
    const setup = await setupApplication();
    app = setup.app;
    client = setup.client;

    userHelperServiceStub = {
      getUserById: sinon
        .stub()
        .resolves(new UserView({id: 'user-2', roleId: 'role-2'})),
    };
    roleHelperServiceStub = {
      getRoles: sinon
        .stub()
        .resolves([new Role({id: 'role-2', name: 'Manager'})]),
    };
    app.bind('services.UserHelperService').to(userHelperServiceStub);
    app.bind('services.RoleHelperService').to(roleHelperServiceStub);
  });

  after(async () => {
    await app.stop();
  });

  it('GET /users/{id} returns user and role', async () => {
    const fakeToken = getToken(
      [PermissionKey.ViewTenantUser, PermissionKey.ViewRoles],
      false,
    );
    const res = await client
      .get('/users/user-2')
      .set('Authorization', fakeToken)
      .expect(200);

    expect(res.body).to.deepEqual({
      user: {id: 'user-2', roleId: 'role-2'},
      role: {id: 'role-2', name: 'Manager'},
    });
    sinon.assert.calledOnce(userHelperServiceStub.getUserById);
    sinon.assert.calledOnce(roleHelperServiceStub.getRoles);
  });
});

describe('UserController PATCH /users/{id}/status (updateUserStatus)', () => {
  let app: AuthenticationFacadeApplication;
  let client: Client;
  let userHelperServiceStub: {updateUserStatus: sinon.SinonStub};

  before('setupApplication', async () => {
    const setup = await setupApplication();
    app = setup.app;
    client = setup.client;

    userHelperServiceStub = {
      updateUserStatus: sinon.stub().resolves(),
    };
    app.bind('services.UserHelperService').to(userHelperServiceStub);
  });

  after(async () => {
    await app.stop();
  });

  it('PATCH /users/{id}/status updates user status', async () => {
    const fakeToken = getToken([PermissionKey.UpdateTenantUser], false);
    await client
      .patch('/users/user-3/status')
      .set('Authorization', fakeToken)
      .send({status: 1})
      .expect(204);

    sinon.assert.calledOnce(userHelperServiceStub.updateUserStatus);
    const [id, dto] = userHelperServiceStub.updateUserStatus.getCall(0).args;
    expect(id).to.equal('user-3');
    expect(dto).to.containDeep({status: 1});
  });
});

describe('UserController POST /users/bulk (createUsersBulk)', () => {
  let app: AuthenticationFacadeApplication;
  let client: Client;
  let userHelperServiceStub: {createBulkUsers: sinon.SinonStub};

  before('setupApplication', async () => {
    const setup = await setupApplication();
    app = setup.app;
    client = setup.client;

    userHelperServiceStub = {
      createBulkUsers: sinon
        .stub()
        .resolves([new User({id: 'user-4', email: '<EMAIL>'})]),
    };
    app.bind('services.UserHelperService').to(userHelperServiceStub);
  });

  after(async () => {
    await app.stop();
  });

  it('POST /users/bulk creates users in bulk', async () => {
    const fakeToken = getToken([PermissionKey.CreateTenantUser], false);
    const res = await client
      .post('/users/bulk')
      .set('Authorization', fakeToken)
      .send([
        {email: '<EMAIL>', fullName: 'Bulk User', roleId: 'roleY'},
      ])
      .expect(200);

    expect(res.body).to.deepEqual([{id: 'user-4', email: '<EMAIL>'}]);
    sinon.assert.calledOnce(userHelperServiceStub.createBulkUsers);
    const [dtos] = userHelperServiceStub.createBulkUsers.getCall(0).args;
    expect(dtos[0]).to.containDeep({
      email: '<EMAIL>',
      fullName: 'Bulk User',
      roleId: 'roleY',
    });
  });
});
/**
 * Acceptance test for POST /users/{id}/resend-invitation (resendInvitationEmail)
 */
describe('UserController POST /users/{id}/resend-invitation (resendInvitationEmail)', () => {
  let app: import('../../application').AuthenticationFacadeApplication;
  let client: import('@loopback/testlab').Client;
  let userHelperServiceStub: {
    resendInvitationEmail: import('@loopback/testlab').sinon.SinonStub;
  };

  before('setupApplication', async () => {
    const setup = await require('./test-helper').setupApplication();
    app = setup.app;
    client = setup.client;

    userHelperServiceStub = {
      resendInvitationEmail: require('@loopback/testlab')
        .sinon.stub()
        .resolves(),
    };
    app.bind('services.UserHelperService').to(userHelperServiceStub);
  });

  after(async () => {
    await app.stop();
  });

  it('POST /users/{id}/resend-invitation resends invitation email', async () => {
    const fakeToken = require('./test-helper').getToken(
      [require('@local/core').PermissionKey.UpdateTenantUser],
      false,
    );
    await client
      .post('/users/user-5/resend-invitation')
      .set('Authorization', fakeToken)
      .expect(204);

    require('@loopback/testlab').sinon.assert.calledOnce(
      userHelperServiceStub.resendInvitationEmail,
    );
    const [id] = userHelperServiceStub.resendInvitationEmail.getCall(0).args;
    require('@loopback/testlab').expect(id).to.equal('user-5');
  });
});
