// Copyright (c) 2025 Sourcefuse Technologies

import {Client, expect, sinon} from '@loopback/testlab';
import {setupApplication, getToken} from './test-helper';
import {AuthenticationFacadeApplication} from '../../application';
import {RoleHelperService} from '../../services';
import {Role} from '@sourceloop/authentication-service';
import {PermissionKey} from '@local/core';

describe('RoleController (acceptance)', () => {
  let app: AuthenticationFacadeApplication;
  let client: Client;
  let roleHelperStub: Partial<RoleHelperService>;

  const role: Role = new Role({
    id: 'role-1',
    name: 'Admin',
  });
  const allStatusesMock = {0: 'Active', 1: 'Inactive'};

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());

    roleHelperStub = {
      getRoles: sinon.stub().resolves([role]),
      getAllRoleStatuses: sinon.stub().resolves(allStatusesMock),
      addRole: sinon.stub().resolves(role),
      updateRole: sinon.stub().resolves(role),
      getAllRoles: sinon.stub().resolves([role]),
    };

    app.bind('services.RoleHelperService').to(roleHelperStub);
  });

  after(async () => {
    await app.stop();
  });

  afterEach(() => {
    sinon.restore();
  });

  const authHeader = () => ({
    Authorization: getToken([PermissionKey.ViewRoles]),
  });

  it('GET /roles returns roles with valid auth', async () => {
    const res = await client.get('/roles').set(authHeader()).expect(200);
    expect(res.body).to.be.Array();
    expect(res.body[0]).to.containEql({id: 'role-1', name: 'Admin'});
    sinon.assert.calledOnce(roleHelperStub.getRoles as sinon.SinonStub);
  });

  it('GET /roles supports filter query', async () => {
    const filter = {where: {name: 'Admin'}};
    await client
      .get('/roles')
      .set(authHeader())
      .query({filter: JSON.stringify(filter)})
      .expect(200);
    sinon.assert.calledWith(
      roleHelperStub.getRoles as sinon.SinonStub,
      sinon.match(filter),
    );
  });

  it('GET /roles returns 401 without auth', async () => {
    await client.get('/roles').expect(401);
  });

  it('GET /roles returns 403 without permission', async () => {
    const token = getToken([]); // No permissions
    await client.get('/roles').set({Authorization: token}).expect(403);
  });

  it('GET /roles propagates errors from helper service', async () => {
    (roleHelperStub.getRoles as sinon.SinonStub).rejects({
      statusCode: 500,
      message: 'Internal error',
    });
    await client.get('/roles').set(authHeader()).expect(500);
  });

  it('GET /roles/all-statuses - should return all statuses', async () => {
    const res = await client
      .get('/roles/all-statuses')
      .set('Authorization', getToken([PermissionKey.ViewRoles]))
      .expect(200);

    expect(res.body).to.containEql(allStatusesMock);
    sinon.assert.calledOnce(
      roleHelperStub.getAllRoleStatuses as sinon.SinonStub,
    );
  });

  it('should return all statues', async () => {
    await client
      .get('/roles/all-statuses')
      .set('Authorization', getToken([]))
      .expect(403);
  });
  // POST /roles - should add a role with valid auth
  it('POST /roles adds a role with valid auth', async () => {
    await client
      .post('/roles')
      .set('Authorization', getToken([PermissionKey.AddRole]))
      .send({name: 'Admin'})
      .expect(200)
      .then(res => {
        expect(res.body).to.containEql({id: 'role-1', name: 'Admin'});
        sinon.assert.calledOnce(roleHelperStub.addRole as sinon.SinonStub);
      });
  });

  // POST /roles - should return 401 without auth
  it('POST /roles returns 401 without auth', async () => {
    await client.post('/roles').send({name: 'Admin'}).expect(401);
  });

  // PATCH /roles/{id} - should update a role with valid auth
  it('PATCH /roles/:id updates a role with valid auth', async () => {
    await client
      .patch('/roles/role-1')
      .set('Authorization', getToken([PermissionKey.UpdateRole]))
      .send({name: 'Admin'})
      .expect(200)
      .then(res => {
        expect(res.body).to.containEql({id: 'role-1', name: 'Admin'});
        sinon.assert.calledOnce(roleHelperStub.updateRole as sinon.SinonStub);
      });
  });

  // PATCH /roles/{id} - should return 401 without auth
  it('PATCH /roles/:id returns 401 without auth', async () => {
    await client.patch('/roles/role-1').send({name: 'Admin'}).expect(401);
  });

  // GET /roles/all - should return all roles with valid auth
  it('GET /roles/all returns all roles with valid auth', async () => {
    const res = await client
      .get('/roles/all')
      .set('Authorization', getToken([PermissionKey.ViewRoles]))
      .expect(200);
    expect(res.body).to.be.Array();
    expect(res.body[0]).to.containEql({id: 'role-1', name: 'Admin'});
    sinon.assert.calledOnce(roleHelperStub.getAllRoles as sinon.SinonStub);
  });

  // GET /roles/all - should return 401 without auth
  it('GET /roles/all returns 401 without auth', async () => {
    await client.get('/roles/all').expect(401);
  });
});
