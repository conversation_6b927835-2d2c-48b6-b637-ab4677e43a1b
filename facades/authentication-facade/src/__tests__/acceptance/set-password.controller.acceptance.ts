// Copyright (c) 2025 Sourcefuse Technologies

import {Client, expect, sinon} from '@loopback/testlab';
import {getToken, setupApplication} from './test-helper';
import {SetPasswordHelperService} from '../../services/set-password-helper.service';
import {AuthenticationFacadeApplication} from '../../application';
import {UpdatePasswordDto} from '../../models/dto';
import {HttpErrors} from '@loopback/rest';
import {PermissionKey} from '@local/core';
import {SinonStubbedInstance} from 'sinon';
import {UserTokenRepository} from '../../repositories/user-token.repository';
import {UserToken} from '../../models';

describe('SetPasswordController Acceptance', () => {
  let app: AuthenticationFacadeApplication;
  let client: Client;
  let setPasswordHelperServiceStub: SinonStubbedInstance<SetPasswordHelperService>;
  let userTokenRepositoryStub: SinonStubbedInstance<UserTokenRepository>;
  let originalEnv: NodeJS.ProcessEnv;

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
  });
  beforeEach(() => {
    sinon.restore();
    originalEnv = {...process.env};

    setPasswordHelperServiceStub = {
      setPassword: sinon.stub().resolves(),
    } as unknown as SinonStubbedInstance<SetPasswordHelperService>;

    userTokenRepositoryStub = {
      set: sinon.stub().resolves(),
      expire: sinon.stub().resolves(),
      delete: sinon.stub().resolves(),
      get: sinon.stub(),
    } as unknown as sinon.SinonStubbedInstance<UserTokenRepository>;

    app
      .bind('services.SetPasswordHelperService')
      .to(setPasswordHelperServiceStub);
    app.bind('repositories.UserTokenRepository').to(userTokenRepositoryStub);
  });

  afterEach(() => {
    process.env = {...originalEnv};
    sinon.restore();
    app.bind('services.SetPasswordHelperService');
    app.bind('repositories.UserTokenRepository');
  });

  after(async () => {
    await app.stop();
  });

  it('POST /auth/set-password/verify - should call setPassword and return 204', async () => {
    const token = getToken([PermissionKey.UpdatePassword]);
    const rawToken = token.replace(/^Bearer\s+/i, '');

    userTokenRepositoryStub.get.resolves(
      new UserToken({
        token: rawToken,
      }),
    );

    const payload: UpdatePasswordDto = new UpdatePasswordDto({
      newPassword: 'newStrongPassword123!',
      // add other required fields if any
    });

    const res = await client
      .post('/auth/set-password/verify')
      .set('Authorization', token)
      .send(payload)
      .expect(204);

    expect(res.body).to.be.empty();
  });

  it('POST /auth/set-password/verify - should return 500 if CLIENT_ID or CLIENT_SECRET missing', async () => {
    delete process.env.CLIENT_ID;
    delete process.env.CLIENT_SECRET;

    const token = getToken([PermissionKey.UpdatePassword]);
    const rawToken = token.replace(/^Bearer\s+/i, '');

    userTokenRepositoryStub.get.resolves(
      new UserToken({
        token: rawToken,
      }),
    );

    const payload: UpdatePasswordDto = new UpdatePasswordDto({
      newPassword: 'newStrongPassword123!',
    });

    (setPasswordHelperServiceStub.setPassword as sinon.SinonStub).rejects(
      new HttpErrors.InternalServerError(
        'CLIENT_ID or CLIENT_SECRET is not defined in environment variables',
      ),
    );

    const res = await client
      .post('/auth/set-password/verify')
      .set('Authorization', token)
      .send(payload)
      .expect(500);

    expect(res.body.error.message).to.match('Internal Server Error');
  });

  it('POST /auth/set-password/verify - should return 400 if Authorization header is missing', async () => {
    process.env.CLIENT_ID = 'test-client';
    process.env.CLIENT_SECRET = 'test-secret';

    const payload: UpdatePasswordDto = new UpdatePasswordDto({
      newPassword: 'newStrongPassword123!',
    });

    (setPasswordHelperServiceStub.setPassword as sinon.SinonStub).rejects(
      new HttpErrors.BadRequest('Authorization header is missing'),
    );

    const res = await client
      .post('/auth/set-password/verify')
      .send(payload)
      .expect(401);

    expect(res.body.error.message).to.match('Bearer realm="Users"');
  });

  it('POST /auth/set-password/verify - should propagate errors from helper service', async () => {
    process.env.CLIENT_ID = 'test-client';
    process.env.CLIENT_SECRET = 'test-secret';

    const token = getToken([PermissionKey.UpdatePassword]);
    const rawToken = token.replace(/^Bearer\s+/i, '');

    userTokenRepositoryStub.get.resolves(
      new UserToken({
        token: rawToken,
      }),
    );

    const payload: UpdatePasswordDto = new UpdatePasswordDto({
      newPassword: 'newStrongPassword123!',
    });

    (setPasswordHelperServiceStub.setPassword as sinon.SinonStub).rejects(
      new HttpErrors.Forbidden('Some forbidden error'),
    );

    const res = await client
      .post('/auth/set-password/verify')
      .set('Authorization', token)
      .send(payload)
      .expect(403);

    expect(res.body.error.message).to.match(/Some forbidden error/);
  });
});
