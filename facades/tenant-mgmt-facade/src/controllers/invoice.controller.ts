import {Permission<PERSON>ey} from '@local/core';
import {inject} from '@loopback/context';
import {
  Filter,
  CountSchema,
  Count,
  Where,
  FilterExcludingWhere,
} from '@loopback/repository';
import {
  get,
  getModelSchemaRef,
  param,
  Request,
  RestBindings,
} from '@loopback/rest';
import {OPERATION_SECURITY_SPEC, STATUS_CODE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {Invoice} from '../models';
import {SubscriptionProxyService} from '../services/proxies';

const basePath = '/invoices';

/**
 * Controller for handling invoice-related operations.
 *
 * This controller provides endpoints to retrieve a list of invoices and to count invoices,
 * delegating the actual data retrieval to the SubscriptionProxyService.
 *
 * @remarks
 * - All endpoints require Bearer authentication and the `ViewTenantBillings` permission.
 * - The controller uses dependency injection for the subscription proxy service and the HTTP request.
 *
 * @constructor
 * @param subscriptionProxyService - Service proxy for subscription and invoice operations.
 * @param request - The current HTTP request object.
 *
 * @method find
 * Retrieves an array of invoices, optionally filtered.
 * @param filter - Optional LoopBack filter for querying invoices.
 * @returns A promise resolving to an array of Invoice instances.
 *
 * @method count
 * Retrieves the count of invoices matching the optional where filter.
 * @param where - Optional LoopBack where filter for counting invoices.
 * @returns A promise resolving to a Count object.
 */
export class InvoiceController {
  constructor(
    @inject('services.SubscriptionProxyService')
    private readonly subscriptionProxyService: SubscriptionProxyService,

    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
  ) {}

  @authorize({
    permissions: [PermissionKey.ViewTenantBillings],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of ConfigureDevice model instances',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(Invoice, {
                includeRelations: true,
              }),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(Invoice) filter?: Filter<Invoice>,
  ): Promise<Invoice[]> {
    const filterString = JSON.stringify(filter);
    const token = this.request.headers.authorization ?? '';
    return this.subscriptionProxyService.getInvoices(token, filterString);
  }

  @authorize({
    permissions: [PermissionKey.ViewTenantBillings],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/count`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Plan model count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async count(@param.where(Invoice) where?: Where<Invoice>): Promise<Count> {
    const whereString = JSON.stringify(where);
    return this.subscriptionProxyService.getInvoicesCount(
      this.request.headers.authorization ?? '',
      whereString,
    );
  }

  /**
   * Retrieves an invoice by its unique identifier.
   *
   * @param id - The unique identifier of the invoice to retrieve.
   * @param filter - Optional filter object to specify fields and relations to include in the result, excluding the 'where' clause.
   * @returns A promise that resolves to the requested Invoice object.
   */
  @authorize({
    permissions: [PermissionKey.ViewTenantBillings],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/{id}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Invoice model instance',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Invoice, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Invoice, {exclude: 'where'})
    filter?: FilterExcludingWhere<Invoice>,
  ): Promise<Invoice> {
    return this.subscriptionProxyService.getInvoiceById(
      this.request.headers.authorization ?? '',
      id,
      filter,
    );
  }

  @authorize({
    permissions: [PermissionKey.DownloadInvoice],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/{id}/pdf-url`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Stripe Invoice PDF URL',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                pdfUrl: {type: 'string', nullable: true},
              },
            },
          },
        },
      },
    },
  })
  /**
   * Retrieves the PDF URL for a Stripe invoice associated with a tenant.
   *
   * @param id - The unique identifier of the tenant.
   * @param stripeInvoiceId - The Stripe invoice ID for which to fetch the PDF URL.
   * @returns An object containing the PDF URL (`pdfUrl`) if available, or `null` if not found.
   */
  async getStripeInvoicePdfUrl(
    @param.path.string('id') id: string,
    @param.query.string('stripeInvoiceId') stripeInvoiceId: string,
  ): Promise<{pdfUrl: string | null}> {
    return this.subscriptionProxyService.getStripeInvoicePdfUrl(
      this.request.headers.authorization ?? '',
      id,
      stripeInvoiceId,
    );
  }
}
