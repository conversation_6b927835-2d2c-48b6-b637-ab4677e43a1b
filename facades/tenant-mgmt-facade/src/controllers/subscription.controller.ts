import {PermissionKey} from '@local/core';
import {inject} from '@loopback/context';
import {Filter} from '@loopback/repository';
import {
  get,
  getModelSchemaRef,
  param,
  Request,
  RestBindings,
} from '@loopback/rest';
import {OPERATION_SECURITY_SPEC, STATUS_CODE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {Subscription} from '../models';
import {SubscriptionProxyService} from '../services/proxies';

const basePath = '/subscriptions';

/**
 * Controller to manage BillingCycle resources related to subscription .
 *
 * Provides endpoints to retrieve BillingCycle entities.
 */
export class SubscriptionController {
  /**
   * Creates an instance of SubscriptionController.
   *
   * @param subscriptionProxyService - Service proxy to interact with subscription data.
   * @param request - Incoming HTTP request object.
   */
  constructor(
    @inject('services.SubscriptionProxyService')
    private readonly subscriptionProxyService: SubscriptionProxyService,

    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
  ) {}

  /**
   * Retrieves an array of BillingCycle model instances.
   *
   * @param filter - Optional filter object to query BillingCycle entities.
   * @returns Promise resolving to an array of BillingCycle instances.
   *
   * @authorization Requires permission: ViewBillingCycle
   * @authentication Bearer token required
   */
  @authorize({
    permissions: [PermissionKey.ViewBillingCycle],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Subscription model instances',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(Subscription, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(Subscription) filter?: Filter<Subscription>,
  ): Promise<Subscription[]> {
    const token = this.request.headers.authorization ?? '';
    return this.subscriptionProxyService.getSubscriptions(
      token,
      JSON.stringify(filter),
    );
  }
}
