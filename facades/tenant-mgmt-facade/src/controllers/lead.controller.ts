import {inject} from '@loopback/core';
import {Count, CountSchema, Filter, Where} from '@loopback/repository';
import {
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  Request,
  requestBody,
  RestBindings,
} from '@loopback/rest';
import {
  CONTENT_TYPE,
  OPERATION_SECURITY_SPEC,
  STATUS_CODE,
} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {CreateLeadDTO, Lead} from '../models';
import {PermissionKey, StatusDto} from '@local/core';
import {TenantMgmtProxyService} from '../services';

const basePath = '/leads';
const leadDescription = 'Lead model instance';

export class LeadController {
  constructor(
    @inject('services.TenantMgmtProxyService')
    private readonly tenantManagementProxyService: TenantMgmtProxyService,

    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
  ) {}

  @authorize({
    permissions: [PermissionKey.CreateLead],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @post(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: leadDescription,
        content: {
          [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Lead)},
        },
      },
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(CreateLeadDTO, {
            title: 'CreateLeadDTO',
            exclude: ['isValidated', 'addressId', 'id'],
          }),
        },
      },
    })
    lead: Omit<CreateLeadDTO, 'isValidated' | 'addressId' | 'id'>,
  ): Promise<Lead> {
    const token = this.request.headers.authorization ?? '';
    return this.tenantManagementProxyService.createLead(token, lead);
  }

  @authorize({
    permissions: [PermissionKey.ViewLead],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/count`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Lead model count',
        content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
      },
    },
  })
  async count(@param.where(Lead) where?: Where<Lead>): Promise<Count> {
    const token = this.request.headers.authorization ?? '';
    return this.tenantManagementProxyService.getLeadsCount(token, where);
  }

  @authorize({
    permissions: [PermissionKey.ViewLead],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Lead model instances',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(Lead, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(@param.filter(Lead) filter?: Filter<Lead>): Promise<Lead[]> {
    const token = this.request.headers.authorization ?? '';
    return this.tenantManagementProxyService.getLeads(token, filter);
  }

  @authorize({
    permissions: [PermissionKey.ViewLead],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/{id}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: leadDescription,
        content: {
          [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Lead)},
        },
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Lead, {exclude: 'where'})
    filter?: Filter<Lead>,
  ): Promise<Lead> {
    const token = this.request.headers.authorization ?? '';
    return this.tenantManagementProxyService.getLeadById(token, id, filter);
  }

  @authorize({
    permissions: [PermissionKey.UpdateLead],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @patch(`${basePath}/{id}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Lead PATCH success',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: getModelSchemaRef(Lead),
          },
        },
      },
    },
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Lead, {partial: true}),
        },
      },
    })
    lead: Lead,
  ): Promise<Lead> {
    const token = this.request.headers.authorization ?? '';
    return this.tenantManagementProxyService.updateLeadById(token, id, lead);
  }

  @authorize({
    permissions: [PermissionKey.ViewAllStatuses],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/all-status`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Object of all possible lead status',
        permissions: [PermissionKey.ViewAllStatuses],
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'object',
              items: getModelSchemaRef(StatusDto, {
                title: 'LeadStatusDto',
              }),
            },
          },
        },
      },
    },
  })
  async findAllStatus(): Promise<StatusDto> {
    const token = this.request.headers.authorization ?? '';
    return this.tenantManagementProxyService.getLeadStatuses(token);
  }
}
