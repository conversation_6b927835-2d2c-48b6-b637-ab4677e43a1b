import {inject, lifeCycleObserver, LifeCycleObserver} from '@loopback/core';
import {juggler} from '@loopback/repository';
import {CONTENT_TYPE} from '@sourceloop/core';
const tokenKey = '{token}';
const config = {
  name: 'SubscriptionService',
  connector: 'rest',
  baseURL: '',
  crud: false,
  options: {
    baseUrl: process.env.SUBSCRIPTION_SERVICE_URL as string,
    headers: {
      accept: CONTENT_TYPE.JSON,
      ['content-type']: CONTENT_TYPE.JSON,
    },
  },
  operations: [
    {
      template: {
        method: 'GET',
        url: '/plans/{id}',
        headers: {
          Authorization: tokenKey,
        },
        query: {
          filter: '{filter}',
        },
      },
      functions: {
        findPlanById: ['token', 'id', 'filter'],
      },
    },

    {
      template: {
        method: 'PATCH',
        url: '/subscriptions/{id}',
        headers: {
          Authorization: tokenKey,
        },

        body: '{body}',
      },
      functions: {
        updateSubscriptionById: ['token', 'id', 'body'],
      },
    },

    {
      template: {
        method: 'GET',
        url: '/subscriptions',
        headers: {
          Authorization: tokenKey,
        },
        query: {
          filter: '{filter}',
        },
      },
      functions: {
        getSubscriptions: ['token', 'filter'],
      },
    },
    {
      template: {
        method: 'GET',
        url: '/billing-cycles',
        headers: {
          Authorization: tokenKey,
        },
        query: {
          filter: '{filter}',
        },
      },
      functions: {
        getSubscriptionTenure: ['token', 'filter'],
      },
    },

    {
      template: {
        method: 'GET',
        url: '/plan-sizes',
        headers: {
          Authorization: tokenKey,
        },
        query: {
          filter: '{filter}',
        },
      },
      functions: {
        getStorageSize: ['token', 'filter'],
      },
    },

    {
      template: {
        method: 'GET',
        url: '/configure-devices',
        headers: {
          Authorization: tokenKey,
        },
        query: {
          filter: '{filter}',
        },
      },
      functions: {
        getConfigureDevices: ['token', 'filter'],
      },
    },
    {
      template: {
        method: 'GET',
        url: '/tenant-billings',
        headers: {
          Authorization: tokenKey,
        },
        query: {
          filter: '{filter}',
        },
      },
      functions: {
        getTenantBillings: ['token', 'filter'],
      },
    },
    {
      template: {
        method: 'GET',
        url: '/tenant-billings/count',
        headers: {
          Authorization: tokenKey,
        },
        query: {
          where: '{where}',
        },
      },
      functions: {
        getTenantBillingsCount: ['token', 'where'],
      },
    },
    {
      template: {
        method: 'GET',
        url: '/tenant-billings/all-status',
        headers: {
          Authorization: tokenKey,
        },
      },
      functions: {
        getAllBillingStatus: ['token'],
      },
    },
    {
      template: {
        method: 'POST',
        url: '/plans',
        headers: {
          Authorization: tokenKey,
        },
        body: '{body}',
      },
      functions: {
        createPlan: ['token', 'body'],
      },
    },
    {
      template: {
        method: 'POST',
        url: '/plan-histories',
        headers: {
          Authorization: tokenKey,
        },
        body: '{body}',
      },
      functions: {
        createPlanHistory: ['token', 'body'],
      },
    },
    {
      template: {
        method: 'POST',
        url: '/prices',
        headers: {
          Authorization: tokenKey,
        },
        body: '{body}',
      },
      functions: {
        createPrice: ['token', 'body'],
      },
    },
    {
      template: {
        method: 'POST',
        url: '/billing-customer',
        headers: {
          Authorization: tokenKey,
          tenantId: '{tenantId}',
        },
        body: '{customerDto}',
      },
      functions: {
        createCustomer: ['token', 'customerDto', 'tenantId'],
      },
    },
    {
      template: {
        method: 'POST',
        url: '/subscriptions',
        headers: {
          Authorization: tokenKey,
        },
        body: '{body}',
      },
      functions: {
        createSubscriptions: ['token', 'body'],
      },
    },
    {
      template: {
        method: 'GET',
        url: '/plans',
        headers: {
          Authorization: tokenKey,
        },
        query: {
          filter: '{filter}',
        },
      },
      functions: {
        getPlans: ['token', 'filter'],
      },
    },
    {
      template: {
        method: 'GET',
        url: '/currencies',
        headers: {
          Authorization: tokenKey,
        },
        query: {
          filter: '{filter}',
        },
      },
      functions: {
        getCurrencies: ['token', 'filter'],
      },
    },
    {
      template: {
        method: 'GET',
        url: '/plans/count',
        headers: {
          Authorization: tokenKey,
        },
        query: {
          where: '{where}',
        },
      },
      functions: {
        getPlansCount: ['token', 'where'],
      },
    },
    {
      template: {
        method: 'GET',
        url: '/plan-histories',
        headers: {
          Authorization: tokenKey,
        },
        query: {
          filter: '{filter}',
        },
      },
      functions: {
        getPlanHistory: ['token', 'filter'],
      },
    },
    {
      template: {
        method: 'GET',
        url: '/plans/all-status',
        headers: {
          Authorization: tokenKey,
        },
      },
      functions: {
        getAllPlanStatus: ['token'],
      },
    },
    {
      template: {
        method: 'PATCH',
        url: '/plans/{id}',
        headers: {
          Authorization: tokenKey,
        },
        body: '{body}',
      },
      functions: {
        updatePlanById: ['token', 'id', 'body'],
      },
    },
    {
      template: {
        method: 'POST',
        url: `/webhooks/payment`,
        headers: {
          'content-type': 'application/json; charset=utf-8',
          'stripe-signature': '{signature}', // Dynamically replaced
        },
        body: '{body}',
      },
      functions: {
        handleWebhook: ['body', 'signature'],
      },
    },
    {
      template: {
        method: 'GET',
        url: '/invoices/{id}',
        headers: {
          Authorization: tokenKey,
        },
        query: {
          filter: '{filter}',
        },
      },
      functions: {
        getInvoiceById: ['token', 'id', 'filter'],
      },
    },
    {
      template: {
        method: 'GET',
        url: '/invoices',
        headers: {
          Authorization: tokenKey,
        },
        query: {
          filter: '{filter}',
        },
      },
      functions: {
        getInvoices: ['token', 'filter'],
      },
    },
    {
      template: {
        method: 'GET',
        url: '/invoices/count',
        headers: {
          Authorization: tokenKey,
        },
        query: {
          where: '{where}',
        },
      },
      functions: {
        getInvoicesCount: ['token', 'where'],
      },
    },
    {
      template: {
        method: 'GET',
        url: '/invoices/{id}/pdf-url',
        headers: {
          Authorization: tokenKey,
        },
        query: {
          stripeInvoiceId: '{stripeInvoiceId}',
        },
      },
      functions: {
        getStripeInvoicePdfUrl: ['token', 'id', 'stripeInvoiceId'],
      },
    },
  ],
};

// Observe application's life cycle to disconnect the datasource when
// application is stopped. This allows the application to be shut down
// gracefully. The `stop()` method is inherited from `juggler.DataSource`.
// Learn more at https://loopback.io/doc/en/lb4/Life-cycle.html
@lifeCycleObserver('datasource')
export class SubscriptionServiceDataSource
  extends juggler.DataSource
  implements LifeCycleObserver
{
  static readonly dataSourceName = 'SubscriptionService';
  static readonly defaultConfig = config;

  constructor(
    @inject('datasources.config.SubscriptionService', {optional: true})
    dsConfig: object = config,
  ) {
    super(dsConfig);
  }
}
