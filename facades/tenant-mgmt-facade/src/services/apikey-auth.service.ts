import {RestBindings, Request, HttpErrors} from '@loopback/rest';
import {webhookHandler} from '../decorators/webhook-handler.decorator';
import {IWebhookHandler} from '../types';
import {inject} from '@loopback/context';
import {
  CryptoHelperService,
  Notification,
  NotificationEventType,
  NotificationType,
  PermissionKey,
  TemplateService,
  TenantStatus,
} from '@local/core';
import {LOGGER, ILogger} from '@sourceloop/core';
import {
  NotificationProxyService,
  SubscriptionProxyService,
  TenantMgmtProxyService,
} from './proxies';
import {service} from '@loopback/core';
import {AnyObject} from '@loopback/repository';

const API_KEY_REQUEST_HEADER = 'x-api-key';

/**
 * Webhook handler service implementation for processing API key events.
 *
 * @remarks
 * - Determines if the incoming webhook request
 */
@webhookHandler()
export class ApiKeyAuthService implements IWebhookHandler {
  /**
   * Creates an instance of ApiKeyAuthService.
   *
   * @param request - Incoming HTTP request
   * @param subscriptionProxyService - Proxy service to handle subscription webhooks
   * @param notificationProxyService - Proxy service to handle notifications
   * @param tenantMgmtProxyService - Proxy service for tenant management
   * @param cryptoHelperService - Service to generate temporary tokens
   * @param templateService - Service to render notification templates
   * @param logger - Logger instance for logging
   */
  constructor(
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @inject('services.SubscriptionProxyService')
    private readonly subscriptionProxyService: SubscriptionProxyService,

    @inject(`services.NotificationProxyService`)
    private readonly notificationProxyService: NotificationProxyService,
    @inject('services.TenantMgmtProxyService')
    private readonly tenantMgmtProxyService: TenantMgmtProxyService,

    @service(CryptoHelperService)
    private readonly cryptoHelperService: CryptoHelperService,

    @service(TemplateService)
    private readonly templateService: TemplateService,
    @inject(LOGGER.LOGGER_INJECT)
    private readonly logger: ILogger,
  ) {}

  /**
   * Checks whether the incoming request is a Stripe webhook event.
   *
   * @returns `true` if the request contains a Stripe signature header, otherwise `false`
   */
  isApplicable(): boolean {
    this.logger.info(
      'Checking apikey webhook is applicable ' +
        this.request.headers[API_KEY_REQUEST_HEADER],
    );

    if (this.request.headers[API_KEY_REQUEST_HEADER]) return true;
    return false;
  }

  /**
   * Handles the API key webhook event.
   *
   * @remarks
   * - Delegates processing to the subscription proxy service.
   * - If the event is `INVOICE_CREATED`, it sends an invoice payment request notification.
   * - If provisioning is required, it triggers tenant provisioning.
   * - Ignores other events.
   *
   * @throws {@link HttpErrors.InternalServerError} If the required notification template is missing
   */
  async handle(): Promise<void> {
    console.info('request body in apikey auth service:---', this.request.body); // NOSONAR
    console.info(
      'request headers in apikey auth service:---',
      this.request.headers,
    ); // NOSONAR

    const buffer = this.request.body;
    const jsonString = buffer.toString('utf8');

    // Parse string -> JSON object
    const parsed = JSON.parse(jsonString);
    const webhookResponse: AnyObject =
      await this.tenantMgmtProxyService.handleWebhookTenantStatus(
        parsed,
        this.request.headers[API_KEY_REQUEST_HEADER],
      );
    // Debug log for full response
    let parsedResponse = webhookResponse;
    if (typeof webhookResponse === 'string') {
      parsedResponse = JSON.parse(webhookResponse);
      console.info('Could not parse parsedResponse as JSON:', parsedResponse); // NOSONAR
    }
    console.info('Full webhook response:', parsedResponse); // NOSONAR

    const token = this.cryptoHelperService.generateTempToken({
      userTenantId: process.env.SYSTEM_USER_TENANT_ID,
      tenantId: process.env.SYSTEM_USER_TENANT_ID,
      defaultTenantId: process.env.SYSTEM_USER_TENANT_ID,
      permissions: [
        PermissionKey.CreateNotification,
        PermissionKey.ViewNotificationTemplate,
      ],
    });
    console.info('Generated token:--------', token); // NOSONAR
    // Fetch email template
    const template = await this.notificationProxyService.getTemplateByName(
      NotificationEventType.TenantOnboarding,
      NotificationType.EMAIL,
      token,
    );
    console.info('Fetched email template:--------', template); // NOSONAR

    if (!template) {
      throw new HttpErrors.InternalServerError(
        'Notification template  not found',
      );
    }
    let emailBody = '',
      emailSubject = '';
    if (Number(parsedResponse.status) === TenantStatus.ACTIVE) {
      emailSubject = 'Tenant Provisioning Successful';
      emailBody = this.templateService.generateEmail(template.body, {
        emailDetails:
          'This is to notify that tenant onboarding is completed. Tenat provisioning completed. Below are the tenant and subscription details.',
        tenantName: parsedResponse.name,
        tenantId: parsedResponse.id,
        subscriptionId: parsedResponse?.subscriptionId ?? '',
        subscriptionPlan: parsedResponse?.subscriptionPlan ?? '',
        subscriptionStatus: parsedResponse?.subscriptionStatus ?? '',
        adminName: `${parsedResponse.contacts[0]?.firstName} ${parsedResponse.contacts[0]?.lastName}`,
        adminEmail: parsedResponse.contacts[0]?.email ?? '',
        passwordLink: '',
        password: ""
      });
    } else if (Number(parsedResponse.status) === TenantStatus.PROVISIONFAILED) {
      emailSubject = 'Tenant Provisioning Failed';
      emailBody = this.templateService.generateEmail(template.body, {
        emailDetails:
          'This is to notify that tenant provisioning is failed. Below are the tenant and subscription details. Your subscription is in Suspended status now.',

        // paymentLink:  'tenant_provision_failed',
        tenantName: parsedResponse.name,
        tenantId: parsedResponse.id,
        subscriptionId: parsedResponse?.subscriptionId ?? '',
        subscriptionPlan: parsedResponse?.subscriptionPlan ?? '',

        adminName: `${parsedResponse.contacts[0]?.firstName} ${parsedResponse.contacts[0]?.lastName}`,
        adminEmail: parsedResponse.contacts[0]?.email ?? '',
      });
    } else {
      // No operation for other events
    }

    const toRecipients = [
      {
        id: parsedResponse.contacts[0]?.id ?? '',
        toEmail: parsedResponse.contacts[0]?.email ?? '',
      },
    ];

    // Add the second recipient only if status is NOT ACTIVE
    if (Number(parsedResponse.status) === TenantStatus.PROVISIONFAILED) {
      toRecipients.push({
        id: parsedResponse.contacts[0]?.id ?? '',
        toEmail: process.env.STAFF_EMAIL ?? '',
      });
    }
    const notification: Notification = new Notification({
      subject: emailSubject,
      body: emailBody,
      receiver: {
        to: toRecipients,
      },
      type: 1,
      sentDate: new Date(),
      options: {
        fromEmail: process.env.NOTIFICATION_FROM_EMAIL,
      },
    });

    await this.notificationProxyService.createNotification(token, notification);
  }
}
