import {Provider, inject} from '@loopback/core';
import {getService} from '@loopback/service-proxy';
import {TenantMgmtServiceDataSource} from '../../datasources';
import {
  Contact,
  KeySuggestionDto,
  Lead,
  Tenant,
  TenantOnboardDTO,
  UpdateTenantDto,
  VerifyKeyDto,
} from '../../models';
import {Count, Filter, FilterExcludingWhere, Where} from '@loopback/repository';
import {StatusDto} from '@local/core';
import {ISubscription} from '@sourceloop/ctrl-plane-tenant-management-service';

/**
 * Interface for proxy-based interaction with the Tenant Management microservice.
 */
export interface TenantMgmtProxyService {
  /**
   * Updates the status of a tenant.
   *
   * @param token - Authorization token for the request.
   * @param id - The ID of the tenant to update.
   * @param payload - The status update payload.
   * @returns A Promise resolving to the updated Tenant instance.
   */
  // updateTenantStatus(token: string, id: string, payload: {status: string}): Promise<Tenant>;
  handleWebhookTenantStatus(
    payload: unknown,
    signature: unknown,
  ): Promise<Tenant>;
  /**
   * Creates a new tenant using the provided onboarding payload.
   *
   * @param token - Authorization token for the request.
   * @param payload - The data required to onboard a new tenant.
   * @param leadId - Optional lead ID associated with the tenant.
   * @returns A Promise resolving to the created Tenant instance.
   */
  createTenant(
    token: string,
    payload: TenantOnboardDTO,
    leadId?: string,
  ): Promise<Tenant>;

  /**
   * Verifies a tenant key and provides suggestions if needed.
   *
   * @param token - Authorization token for the request.
   * @param payload - The key verification input payload.
   * @returns A Promise resolving to a KeySuggestionDto, including validation result or suggestions.
   */
  verifyKey(token: string, payload: VerifyKeyDto): Promise<KeySuggestionDto>;

  /**
   * Retrieves a tenant by its ID.
   *
   * @param token - Authorization token for the request.
   * @param id - The ID of the tenant to retrieve.
   * @returns A Promise resolving to the Tenant instance with contact details.
   */
  getTenantById(
    token: string,
    id: string,
    filter?: Filter<Tenant>,
  ): Promise<Tenant>;

  getTenant(token: string, filter?: Filter<Tenant>): Promise<Tenant[]>;
  getTenantCount(token: string, where?: Where<Tenant>): Promise<Count>;

  /**
   * Retrieves the status of all tenants.
   *
   * @param token - Authorization token for the request.
   * @returns A Promise resolving to an array of TenantStatusDto objects.
   */
  getAllTenantStatus(token: string): Promise<StatusDto[]>;

  provisionTenant(
    token: string,
    id: string,
    payload: ISubscription,
  ): Promise<void>;

  updateContactById(
    token: string,
    tenantId: string,
    contact: Partial<Contact>,
  ): Promise<Contact>;
  updateTenantById(
    token: string,
    tenantId: string,
    tenant: Partial<UpdateTenantDto>,
  ): Promise<Tenant>;
  getLeads(token: string, filter?: Filter<Lead>): Promise<Lead[]>;
  createLead(token: string, body: Partial<Lead>): Promise<Lead>;
  getLeadsCount(token: string, where?: Where<Lead>): Promise<Count>;
  updateLeadById(token: string, id: string, body: Partial<Lead>): Promise<Lead>;
  getLeadById(
    token: string,
    id: string,
    filter?: FilterExcludingWhere<Lead>,
  ): Promise<Lead>;
  getLeadStatuses(token: string): Promise<StatusDto>;

  findAllStatusMetrics(
    token: string,
  ): Promise<Record<number, {count: number; status: string}>>;
}

/**
 * Provider class that supplies a proxy implementation of the TenantMgmtProxyService,
 * backed by the configured TenantMgmtServiceDataSource.
 */
export class TenantMgmtProxyServiceProvider
  implements Provider<TenantMgmtProxyService>
{
  /**
   * Creates an instance of the provider.
   *
   * @param dataSource - The injected data source used to create the proxy service.
   */
  constructor(
    @inject('datasources.TenantMgmtService')
    protected dataSource: TenantMgmtServiceDataSource,
  ) {}

  /**
   * Returns the proxy service used to interact with the tenant management microservice.
   *
   * @returns A Promise that resolves to the TenantMgmtProxyService instance.
   */
  value(): Promise<TenantMgmtProxyService> {
    return getService(this.dataSource);
  }
}
