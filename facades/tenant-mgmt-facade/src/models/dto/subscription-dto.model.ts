import {model, property} from '@loopback/repository';
import {Subscription} from '../subscription.model';
import {ProrationBehaviour} from '@local/core';

/**
 * Data Transfer Object (DTO) for Subscription.
 *
 * Extends the base Subscription model and adds additional
 * properties specific to this context.
 */
@model()
export class SubscriptionDto extends Subscription {
  /**
   * Specifies how subscription proration should be handled.
   *
   * Allowed values:
   * - `create_prorations`: Create prorations when updating subscriptions.
   * - `none`: Do not create prorations.
   * - `always_invoice`: Always invoice for prorations immediately.
   */
  @property({
    type: 'string',
    jsonSchema: {
      enum: Object.values(ProrationBehaviour),
    },
  })
  prorationBehavior?: ProrationBehaviour;

  constructor(data?: Partial<SubscriptionDto>) {
    super(data);
  }
}
