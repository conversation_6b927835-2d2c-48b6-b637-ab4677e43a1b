import {getJsonSchema} from '@loopback/openapi-v3';
import {Model, model, property} from '@loopback/repository';
import {FileObject} from './file-dto.model';
import {Contact} from '../contact.model';

@model({
  description: 'model describing payload used to create and onboard a tenant',
})
export class TenantOnboardDTO extends Model {
  @property({
    type: 'object',
    description:
      'metadata for the contact to be created, it is required when tenant is created without a lead',
    jsonSchema: getJsonSchema(Contact, {
      exclude: ['tenantId', 'id'],
    }),
  })
  contact: Omit<Contact, 'id' | 'tenantId'>;

  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      minLength: 3,
      maxLength: 50,
      pattern: "^(?![-\\s])[a-zA-Z0-9\\s&.,'’-]+(?<![-\\s])$",
      errorMessage: {
        pattern:
          "Invalid name format. Use only letters, numbers, spaces, &, ., ,, ', and –. It cannot start or end with a space or hyphen.",
      },
    },
  })
  name: string;

  @property({
    type: 'string',
    description: 'address of the tenant owners',
  })
  address?: string;

  @property({
    type: 'string',
    description: 'city of the tenant owner',
  })
  city?: string;

  @property({
    description: 'state of the tenant owner',
    type: 'string',
  })
  state?: string;

  @property({
    description: 'zip code of the tenant owner',
    type: 'string',
  })
  zip?: string;

  @property({
    type: 'string',
    description: 'country of the tenant owner',
  })
  country?: string;

  @property({
    type: 'string',
    description:
      'A short string used to identify a tenant. This is also used as the namespace and subdomain for this particular tenant.',
    required: true,
    jsonSchema: {
      pattern: '^(?!-)[a-zA-Z0-9-]{3,63}(?<!-)$',
      minLength: 3,
      maxLength: 63,
    },
  })
  key: string;

  @property({
    required: true,
    jsonSchema: {
      type: 'array',
      uniqueItems: true,
      items: {
        type: 'string',
        format: 'hostname',
      },
    },
  })
  domains: string[];

  @property({
    type: 'string',
    name: 'lang',
    required: true,
    jsonSchema: {
      enum: ['English'], // Only allow 'english' as a valid value
    },
  })
  lang: string;

  @property({
    type: 'array',
    itemType: 'object',
    name: 'files',
    required: true,
  })
  files: FileObject[];

  @property({
    type: 'string',
    name: 'plan_name',
  })
  planName?: string;

  @property({
    name: 'plan_id',
    description: 'id of the plan associated with this tenant.',
    type: 'string',
  })
  planId?: string;

  constructor(data?: Partial<TenantOnboardDTO>) {
    super(data);
  }
}
