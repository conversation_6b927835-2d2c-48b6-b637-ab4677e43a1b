import {model, property} from '@loopback/repository';
import {FileObject} from './file-dto.model';
import {Tenant} from '../tenant.model';
import {Contact} from '../contact.model';
import {getJsonSchema} from '@loopback/openapi-v3';

/**
 * Data Transfer Object (DTO) for updating a tenant.
 *
 * Extends the base {@link Tenant} model and adds additional fields
 * required during tenant onboarding or update processes.
 */
@model({
  description: 'model describing payload used to create and onboard a tenant',
})
export class UpdateTenantDto extends Tenant {
  /**
   * List of files selected for the tenant (e.g., documents, images).
   *
   * @type {FileObject[]}
   * @required
   */
  @property({
    type: 'array',
    itemType: 'object',
    name: 'files',
    required: true,
  })
  selectedFiles: FileObject[];

  /**
   * Metadata for the tenant’s primary contact person.
   *
   * - Excludes `tenantId`, `id`, and `email` fields from the `Contact` model.
   *
   * @type {Omit<Contact, 'id' | 'tenantId' | 'email'>}
   */
  @property({
    type: 'object',
    description: 'metadata for the contact to be created.',
    jsonSchema: getJsonSchema(Contact, {
      exclude: ['tenantId', 'id', 'email'],
    }),
  })
  contact: Omit<Contact, 'id' | 'tenantId' | 'email'>;

  /**
   * City of the tenant admin.
   *
   * @type {string | undefined}
   */
  @property({
    type: 'string',
    description: 'city of the tenant admin',
  })
  city?: string;

  /**
   * State of the tenant admin.
   *
   * @type {string | undefined}
   */
  @property({
    description: 'state of the tenant admin',
    type: 'string',
  })
  state?: string;

  /**
   * Creates an instance of {@link UpdateTenantDto}.
   *
   * @param {Partial<UpdateTenantDto>} [data] - Partial initialization data.
   */
  constructor(data?: Partial<UpdateTenantDto>) {
    super(data);
  }
}
