// Acceptance tests for LeadController (Facade)

import {Client, expect} from '@loopback/testlab';
import {getToken, setupApplication} from './test-helper';
import sinon from 'sinon';
import {TenantMgmtFacadeApplication} from '../../application';
import {TenantMgmtProxyService} from '../../services/proxies';
import {PermissionKey, LeadStatus} from '@local/core';

const basePath = '/leads';

describe('LeadController', () => {
  let app: TenantMgmtFacadeApplication;
  let client: Client;
  let proxyStub: sinon.SinonStubbedInstance<TenantMgmtProxyService>;

  const mockLead = {
    id: 'lead1',
    firstName: 'John',
    lastName: 'Doe',
    companyName: 'Acme Corp',
    email: '<EMAIL>',
    designation: 'Manager',
    phoneNumber: '1234567890',
    countryCode: '+91',
    status: LeadStatus.PENDING,
    addressId: 'addr1',
    isValidated: false,
    tenant: {
      id: 'tenant1',
      name: 'Tenant Name',
      status: 0,
      key: 'tenant-key',
      domains: [],
      contacts: [],
      resources: [],
      addressId: 'addr1',
      lang: '',
      planName: '',
      planId: '',
      files: [],
      getId: () => 'tenant1',
      getIdObject: () => ({id: 'tenant1'}),
      toJSON: function () {
        return {...this};
      },
      toObject: () => ({}),
    },
    getId: () => 'lead1',
    getIdObject: () => ({id: 'lead1'}),
    toJSON: function () {
      return {...this};
    },
    toObject: () => ({}),
  };

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
  });

  beforeEach(async () => {
    proxyStub = {
      createTenant: sinon.stub(),
      verifyKey: sinon.stub(),
      getTenantById: sinon.stub(),
      getTenant: sinon.stub(),
      getTenantCount: sinon.stub(),
      getAllTenantStatus: sinon.stub(),
      provisionTenant: sinon.stub(),
      updateContactById: sinon.stub(),
      updateTenantById: sinon.stub(),
      getLeads: sinon.stub(),
      createLead: sinon.stub(),
      getLeadsCount: sinon.stub(),
      updateLeadById: sinon.stub(),
      getLeadById: sinon.stub(),
      getLeadStatuses: sinon.stub(),
      findAllStatusMetrics: sinon.stub(),
      handleWebhookTenantStatus: sinon.stub(),
    };
    app.bind('services.TenantMgmtProxyService').to(proxyStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  after(async () => {
    await app.stop();
  });

  it('invokes POST /leads with valid token', async () => {
    const token = getToken([PermissionKey.CreateLead]);
    proxyStub.createLead.resolves(mockLead);
    const {body} = await client
      .post(basePath)
      .set('Authorization', token)
      .send({
        firstName: mockLead.firstName,
        lastName: mockLead.lastName,
        companyName: mockLead.companyName,
        email: mockLead.email,
        designation: mockLead.designation,
        phoneNumber: mockLead.phoneNumber,
        countryCode: mockLead.countryCode,
        status: mockLead.status,
      })
      .expect(200);
    expect(body.id).to.eql('lead1');
    expect(body.email).to.eql(mockLead.email);
  });

  it('invokes GET /leads/count and returns the count of leads', async () => {
    const token = getToken([PermissionKey.ViewLead]);
    proxyStub.getLeadsCount.resolves({count: 1});
    const {body} = await client
      .get(`${basePath}/count`)
      .set('Authorization', token)
      .expect(200);
    expect(body.count).to.eql(1);
  });

  it('invokes GET /leads and returns an array of lead instances', async () => {
    const token = getToken([PermissionKey.ViewLead]);
    proxyStub.getLeads.resolves([mockLead]);
    const {body} = await client
      .get(basePath)
      .set('Authorization', token)
      .expect(200);
    expect(body).to.be.an.Array();
    expect(body[0].id).to.eql('lead1');
  });

  it('invokes GET /leads/{id} and returns a lead instance', async () => {
    const token = getToken([PermissionKey.ViewLead]);
    proxyStub.getLeadById.resolves(mockLead);
    const {body} = await client
      .get(`${basePath}/lead1`)
      .set('Authorization', token)
      .expect(200);
    expect(body.id).to.eql('lead1');
    expect(body.email).to.eql(mockLead.email);
  });

  it('invokes PATCH /leads/{id} and returns updated lead', async () => {
    const token = getToken([PermissionKey.UpdateLead]);
    const updatedLead = {...mockLead, status: LeadStatus.CONVERTED};
    proxyStub.updateLeadById.resolves(updatedLead);
    const {body} = await client
      .patch(`${basePath}/lead1`)
      .set('Authorization', token)
      .send({status: LeadStatus.CONVERTED})
      .expect(200);
    expect(body.status).to.eql(LeadStatus.CONVERTED);
  });

  it('invokes GET /leads/all-status and returns all possible lead statuses', async () => {
    const token = getToken([PermissionKey.ViewAllStatuses]);
    proxyStub.getLeadStatuses.resolves({
      statuses: {
        [LeadStatus.PENDING]: 'PENDING',
        [LeadStatus.CONVERTED]: 'CONVERTED',
        [LeadStatus.INVALID]: 'INVALID',
      },
      toJSON: function () {
        return this;
      },
      toObject: () => ({}),
    });
    const {body} = await client
      .get(`${basePath}/all-status`)
      .set('Authorization', token)
      .expect(200);
    expect(body.statuses).to.have.property(String(LeadStatus.PENDING));
    expect(body.statuses).to.have.property(String(LeadStatus.CONVERTED));
    expect(body.statuses).to.have.property(String(LeadStatus.INVALID));
  });
});
