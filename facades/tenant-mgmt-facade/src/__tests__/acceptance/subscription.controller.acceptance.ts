import {Client, expect, sinon} from '@loopback/testlab';
import {getToken, setupApplication} from './test-helper';
import {PermissionKey} from '@local/core';
import {TenantMgmtFacadeApplication} from '../../application';
import {SubscriptionProxyService} from '../../services/proxies';
import {Plan, Subscription} from '../../models';
import {SubscriptionStatus} from '@sourceloop/ctrl-plane-subscription-service';

describe('SubscriptionController', () => {
  let app: TenantMgmtFacadeApplication;
  let client: Client;
  let subscriptionServiceProxyServiceStub: sinon.SinonStubbedInstance<SubscriptionProxyService>;

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
  });

  after(async () => {
    await app.stop();
  });

  beforeEach(() => {
    subscriptionServiceProxyServiceStub = {
      findPlanById: sinon.stub(),
      createCustomer: sinon.stub(),
      getTenantBillings: sinon.stub(),
      getTenantBillingsCount: sinon.stub(),
      getAllBillingStatus: sinon.stub(),
      handleWebhook: sinon.stub(),
      getConfigureDevices: sinon.stub(),
      getStorageSize: sinon.stub(),
      createPrice: sinon.stub(),
      getSubscriptionTenure: sinon.stub(),
      getPlans: sinon.stub(),
      createPlan: sinon.stub(),
      getSubscriptions: sinon.stub(),
      updateSubscriptionById: sinon.stub(),
      getCurrencies: sinon.stub(),
      getPlansCount: sinon.stub(),
      getPlanHistory: sinon.stub(),
      getAllPlanStatus: sinon.stub(),
      createSubscriptions: sinon.stub(),
      updatePlanById: sinon.stub(),
      createPlanHistory: sinon.stub(),
      getInvoices: sinon.stub(),
      getInvoicesCount: sinon.stub(),
      getInvoiceById: sinon.stub(),
      getStripeInvoicePdfUrl: sinon.stub(),
    };

    app
      .bind('services.SubscriptionProxyService')
      .to(subscriptionServiceProxyServiceStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  it('returns list of subscriptions when authorized', async () => {
    const token = getToken([PermissionKey.ViewBillingCycle]);

    const mockSubscriptions: (Subscription & {plan: Plan})[] = [
      {
        id: 'sub_1',
        planId: 'plan_1',
        status: SubscriptionStatus.ACTIVE,
        plan: {id: 'plan1'},
      } as Subscription & {plan: Plan},
      {
        id: 'sub_2',
        planId: 'plan_2',
        status: SubscriptionStatus.ACTIVE,
        plan: {id: 'plan2'},
      } as Subscription & {plan: Plan},
    ];

    subscriptionServiceProxyServiceStub.getSubscriptions.resolves(
      mockSubscriptions,
    );

    const res = await client
      .get('/subscriptions')
      .set('Authorization', token)
      .expect(200);

    expect(res.body).to.deepEqual(
      mockSubscriptions.map(s =>
        typeof s.toJSON === 'function' ? s.toJSON() : {...s},
      ),
    );
  });

  it('returns empty array when no subscriptions found', async () => {
    const token = getToken([PermissionKey.ViewBillingCycle]);
    subscriptionServiceProxyServiceStub.getSubscriptions.resolves([]);

    const res = await client
      .get('/subscriptions')
      .set('Authorization', token)
      .expect(200);

    expect(res.body).to.deepEqual([]);
  });

  it('fails with 401 when no token is provided', async () => {
    await client.get('/subscriptions').expect(401);
    sinon.assert.notCalled(
      subscriptionServiceProxyServiceStub.getSubscriptions,
    );
  });
});
