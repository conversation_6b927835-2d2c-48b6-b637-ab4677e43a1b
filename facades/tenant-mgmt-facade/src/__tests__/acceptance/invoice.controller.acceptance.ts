// Copyright (c) 2025
import {Client, expect} from '@loopback/testlab';
import {getToken} from './test-helper';
import sinon from 'sinon';
import {TenantMgmtFacadeApplication} from '../../application';
import {SubscriptionProxyService} from '../../services/proxies';
import {HTTP_STATUS, InvoiceStatus, PermissionKey} from '@local/core';
import {Invoice} from '../../models';

const basePath = '/invoices';

let subscriptionServiceProxyServiceStub: sinon.SinonStubbedInstance<SubscriptionProxyService>;

describe('BillingInvoiceController', () => {
  let app: TenantMgmtFacadeApplication;
  let client: Client;

  before('setupApplication', async () => {
    subscriptionServiceProxyServiceStub = {
      getInvoices: sinon.stub(),
      getInvoicesCount: sinon.stub(),
      getInvoiceById: sinon.stub(),
      findPlanById: sinon.stub(),
      getConfigureDevices: sinon.stub(),
      getStorageSize: sinon.stub(),
      getSubscriptionTenure: sinon.stub(),
      getPlans: sinon.stub(),
      createPlan: sinon.stub(),
      getCurrencies: sinon.stub(),
      getPlansCount: sinon.stub(),
      getPlanHistory: sinon.stub(),
      getAllPlanStatus: sinon.stub(),
      createCustomer: sinon.stub(),
      getTenantBillings: sinon.stub(),
      getTenantBillingsCount: sinon.stub(),
      getAllBillingStatus: sinon.stub(),
      handleWebhook: sinon.stub(),
      createPrice: sinon.stub(),
      createSubscriptions: sinon.stub(),
      updatePlanById: sinon.stub(),
      createPlanHistory: sinon.stub(),
      updateSubscriptionById: sinon.stub(),
      getSubscriptions: sinon.stub(),
      getStripeInvoicePdfUrl: sinon.stub(),
    };
    ({app, client} = await require('./test-helper').setupApplication());
  });

  beforeEach(async () => {
    subscriptionServiceProxyServiceStub.getInvoices.reset();
    subscriptionServiceProxyServiceStub.getInvoicesCount.reset();

    app
      .bind('services.SubscriptionProxyService')
      .to(subscriptionServiceProxyServiceStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  after(async () => {
    await app.stop();
  });

  it('invokes GET /invoices with valid token', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    const now = new Date();
    const invoicesMock = [
      {
        id: 'inv-1',
        invoiceId: 'INV-001',
        billingCustomerId: 'cust-1',
        amount: 1000,
        tax: 100,
        discount: 0,
        dueDate: new Date(
          now.getTime() + 7 * 24 * 60 * 60 * 1000,
        ).toISOString(),
        invoiceStatus: 'paid',
        createdOn: now.toISOString(),
      },
      {
        id: 'inv-2',
        invoiceId: 'INV-002',
        billingCustomerId: 'cust-2',
        amount: 2000,
        tax: 200,
        discount: 50,
        dueDate: new Date(
          now.getTime() + 14 * 24 * 60 * 60 * 1000,
        ).toISOString(),
        invoiceStatus: 'open',
        createdOn: now.toISOString(),
      },
    ];
    subscriptionServiceProxyServiceStub.getInvoices.resolves(
      invoicesMock.map(obj => {
        const inst = new Invoice({
          ...obj,
          invoiceStatus: obj.invoiceStatus as InvoiceStatus,
          createdOn: new Date(obj.createdOn),
        });
        inst.toJSON = function () {
          return {...obj};
        };
        inst.toObject = function () {
          return {...obj};
        };
        return inst;
      }),
    );

    const {body} = await client
      .get(basePath)
      .set('Authorization', `${token}`)
      .expect(HTTP_STATUS.OK);

    expect(body).to.containDeep(invoicesMock);
  });

  it('covers token extraction and service call in GET /invoice', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    const filter = {where: {amount: 1000}};
    const expected = [
      {
        id: 'inv-3',
        invoiceId: 'INV-003',
        billingCustomerId: 'cust-3',
        amount: 1000,
        tax: 100,
        discount: 0,
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        invoiceStatus: 'paid',
        createdOn: new Date().toISOString(),
      },
    ];
    subscriptionServiceProxyServiceStub.getInvoices.resolves(
      expected.map(obj => {
        const inst = new Invoice({
          ...obj,
          invoiceStatus: obj.invoiceStatus as InvoiceStatus,
          createdOn: new Date(obj.createdOn),
        });
        inst.toJSON = function () {
          return {...obj};
        };
        inst.toObject = function () {
          return {...obj};
        };
        return inst;
      }),
    );
    const {body} = await client
      .get(basePath)
      .set('Authorization', `${token}`)
      .query({filter: JSON.stringify(filter)})
      .expect(HTTP_STATUS.OK);

    expect(
      subscriptionServiceProxyServiceStub.getInvoices.calledOnce,
    ).to.be.true();
    expect(
      subscriptionServiceProxyServiceStub.getInvoices.firstCall.args[0],
    ).to.equal(token);
    expect(body).to.containDeep(expected);
  });

  it('covers token extraction and service call in GET /invoices/count', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    const where = {amount: 1000};
    const expected = {count: 5};
    const spy =
      subscriptionServiceProxyServiceStub.getInvoicesCount.resolves(expected);

    const {body} = await client
      .get(`${basePath}/count`)
      .set('Authorization', `${token}`)
      .query({where: JSON.stringify(where)})
      .expect(HTTP_STATUS.OK);

    expect(spy.calledOnce).to.be.true();
    expect(spy.firstCall.args[0]).to.equal(token);
    expect(body).to.eql(expected);
  });
  it('responds with 401 Unauthorized for missing token on GET /invoices', async () => {
    await client.get(basePath).expect(HTTP_STATUS.UNAUTHORIZED);
  });

  it('invokes GET /invoices/count with valid token', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    const countMock = {count: 2};
    subscriptionServiceProxyServiceStub.getInvoicesCount.resolves(countMock);

    const {body} = await client
      .get(`${basePath}/count`)
      .set('Authorization', `${token}`)
      .expect(HTTP_STATUS.OK);

    expect(body).to.eql(countMock);
  });

  it('responds with 401 Unauthorized for missing token on GET /invoices/count', async () => {
    await client.get(`${basePath}/count`).expect(HTTP_STATUS.UNAUTHORIZED);
  });
  it('invokes GET /invoices with filter param', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    const filter = {where: {invoiceStatus: 'paid'}};
    subscriptionServiceProxyServiceStub.getInvoices.resolves([
      (() => {
        const obj = {
          id: 'inv-1',
          invoiceId: 'INV-001',
          billingCustomerId: 'cust-1',
          amount: 1000,
          tax: 100,
          discount: 0,
          dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          invoiceStatus: 'paid' as InvoiceStatus,
          createdOn: new Date(),
        };
        const inst = new Invoice(obj);
        inst.toJSON = function () {
          return {...obj, createdOn: obj.createdOn.toISOString()};
        };
        inst.toObject = function () {
          return {...obj, createdOn: obj.createdOn.toISOString()};
        };
        return inst;
      })(),
    ]);
    const {body} = await client
      .get(basePath)
      .set('Authorization', `${token}`)
      .query({filter: JSON.stringify(filter)})
      .expect(HTTP_STATUS.OK);

    expect(body).to.be.an.Array();
    expect(body[0].invoiceStatus).to.eql('paid');
  });

  it('invokes GET /invoices/count with where param', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    const where = {invoiceStatus: 'paid'};
    subscriptionServiceProxyServiceStub.getInvoicesCount.resolves({count: 1});

    const {body} = await client
      .get(`${basePath}/count`)
      .set('Authorization', `${token}`)
      .query({where: JSON.stringify(where)})
      .expect(HTTP_STATUS.OK);

    expect(body.count).to.eql(1);
  });

  it('handles error in GET /invoices', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    subscriptionServiceProxyServiceStub.getInvoices.rejects(
      new Error('Test error'),
    );
    await client.get(basePath).set('Authorization', `${token}`).expect(500);
  });

  it('handles error in GET /invoices/count', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    subscriptionServiceProxyServiceStub.getInvoicesCount.rejects(
      new Error('Test error'),
    );
    await client
      .get(`${basePath}/count`)
      .set('Authorization', `${token}`)
      .expect(500);
  });

  it('handles empty authorization header in GET /invoices', async () => {
    subscriptionServiceProxyServiceStub.getInvoices.resolves([]);

    await client
      .get(basePath)
      .set('Authorization', '') // Empty authorization header
      .expect(HTTP_STATUS.UNAUTHORIZED);
  });

  it('handles empty authorization header in GET /invoices/count', async () => {
    subscriptionServiceProxyServiceStub.getInvoicesCount.resolves({count: 0});

    await client
      .get(`${basePath}/count`)
      .set('Authorization', '') // Empty authorization header
      .expect(HTTP_STATUS.UNAUTHORIZED);
  });

  it('invokes GET /invoices with complex filter', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    const complexFilter = {
      where: {
        and: [
          {amount: {gte: 1000}},
          {invoiceStatus: 'paid'},
          {createdOn: {gte: new Date('2024-01-01').toISOString()}},
        ],
      },
      order: ['createdOn DESC'],
      limit: 10,
      skip: 0,
    };

    const mockInvoices = [
      {
        id: 'inv-complex',
        invoiceId: 'INV-COMPLEX',
        billingCustomerId: 'cust-complex',
        amount: 1500,
        tax: 150,
        discount: 0,
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        invoiceStatus: 'paid',
        createdOn: new Date().toISOString(),
      },
    ];

    subscriptionServiceProxyServiceStub.getInvoices.resolves(
      mockInvoices.map(obj => {
        const inst = new Invoice({
          ...obj,
          invoiceStatus: obj.invoiceStatus as InvoiceStatus,
          createdOn: new Date(obj.createdOn),
        });
        inst.toJSON = function () {
          return {...obj};
        };
        inst.toObject = function () {
          return {...obj};
        };
        return inst;
      }),
    );

    const {body} = await client
      .get(basePath)
      .set('Authorization', `${token}`)
      .query({filter: JSON.stringify(complexFilter)})
      .expect(HTTP_STATUS.OK);

    expect(
      subscriptionServiceProxyServiceStub.getInvoices.calledOnce,
    ).to.be.true();
    expect(
      subscriptionServiceProxyServiceStub.getInvoices.firstCall.args[0],
    ).to.equal(token);
    expect(subscriptionServiceProxyServiceStub.getInvoices.firstCall.args[1]);
    expect(
      typeof subscriptionServiceProxyServiceStub.getInvoices.firstCall
        .args[1] === 'string'
        ? subscriptionServiceProxyServiceStub.getInvoices.firstCall.args[1]
        : JSON.stringify(
            subscriptionServiceProxyServiceStub.getInvoices.firstCall.args[1],
          ),
    ).to.equal(JSON.stringify(complexFilter));
    expect(body).to.containDeep(mockInvoices);
  });

  it('invokes GET /invoices/count with complex where clause', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    const complexWhere = {
      and: [
        {amount: {gte: 1000}},
        {invoiceStatus: {inq: ['paid', 'open']}},
        {createdOn: {gte: new Date('2024-01-01').toISOString()}},
      ],
    };

    subscriptionServiceProxyServiceStub.getInvoicesCount.resolves({count: 5});

    const {body} = await client
      .get(`${basePath}/count`)
      .set('Authorization', `${token}`)
      .query({where: JSON.stringify(complexWhere)})
      .expect(HTTP_STATUS.OK);

    expect(
      subscriptionServiceProxyServiceStub.getInvoicesCount.calledOnce,
    ).to.be.true();
    expect(
      subscriptionServiceProxyServiceStub.getInvoicesCount.firstCall.args[0],
    ).to.equal(token);
    expect(
      subscriptionServiceProxyServiceStub.getInvoicesCount.firstCall.args[1],
    );
    expect(
      typeof subscriptionServiceProxyServiceStub.getInvoicesCount.firstCall
        .args[1] === 'string'
        ? subscriptionServiceProxyServiceStub.getInvoicesCount.firstCall.args[1]
        : JSON.stringify(
            subscriptionServiceProxyServiceStub.getInvoicesCount.firstCall
              .args[1],
          ),
    ).to.equal(JSON.stringify(complexWhere));
    expect(body.count).to.eql(5);
  });

  it('handles null/undefined filter in GET /invoices', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    subscriptionServiceProxyServiceStub.getInvoices.resolves([]);

    const {body} = await client
      .get(basePath)
      .set('Authorization', `${token}`)
      .expect(HTTP_STATUS.OK);

    expect(
      subscriptionServiceProxyServiceStub.getInvoices.calledOnce,
    ).to.be.true();
    expect(
      subscriptionServiceProxyServiceStub.getInvoices.firstCall.args[0],
    ).to.equal(token);
    expect(
      subscriptionServiceProxyServiceStub.getInvoices.firstCall.args[1],
    ).to.be.undefined();
    expect(body).to.eql([]);
  });

  it('handles null/undefined where in GET /invoices/count', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    subscriptionServiceProxyServiceStub.getInvoicesCount.resolves({count: 0});

    const {body} = await client
      .get(`${basePath}/count`)
      .set('Authorization', `${token}`)
      .expect(HTTP_STATUS.OK);

    expect(
      subscriptionServiceProxyServiceStub.getInvoicesCount.calledOnce,
    ).to.be.true();
    expect(
      subscriptionServiceProxyServiceStub.getInvoicesCount.firstCall.args[0],
    ).to.equal(token);
    expect(
      subscriptionServiceProxyServiceStub.getInvoicesCount.firstCall.args[1],
    ).to.be.undefined();
    expect(body.count).to.eql(0);
  });

  it('handles undefined authorization header in GET /invoices', async () => {
    subscriptionServiceProxyServiceStub.getInvoices.resolves([]);

    await client
      .get(basePath)
      // Don't set Authorization header at all to test undefined case
      .expect(HTTP_STATUS.UNAUTHORIZED);
  });

  it('handles undefined authorization header in GET /invoices/count', async () => {
    subscriptionServiceProxyServiceStub.getInvoicesCount.resolves({count: 0});

    await client
      .get(`${basePath}/count`)
      // Don't set Authorization header at all to test undefined case
      .expect(HTTP_STATUS.UNAUTHORIZED);
  });
  describe('GET /invoices/{id}', () => {
    const invoiceId = 'inv-42';
    const invoiceMock = {
      id: invoiceId,
      invoiceId: 'INV-042',
      billingCustomerId: 'cust-42',
      amount: 4200,
      tax: 420,
      discount: 42,
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      invoiceStatus: 'paid',
      createdOn: new Date().toISOString(),
    };

    it('returns invoice by id with valid token and permission', async () => {
      const token = getToken([PermissionKey.ViewTenantBillings]);
      subscriptionServiceProxyServiceStub.getInvoiceById.resolves(
        new Invoice({
          ...invoiceMock,
          invoiceStatus: invoiceMock.invoiceStatus as InvoiceStatus,
          createdOn: new Date(invoiceMock.createdOn),
        }),
      );
      const {body} = await client
        .get(`${basePath}/${invoiceId}`)
        .set('Authorization', `${token}`)
        .expect(HTTP_STATUS.OK);
      expect(body).to.containDeep(invoiceMock);
      expect(
        subscriptionServiceProxyServiceStub.getInvoiceById.calledOnce,
      ).to.be.true();
      expect(
        subscriptionServiceProxyServiceStub.getInvoiceById.firstCall.args[0],
      ).to.equal(token);
      expect(
        subscriptionServiceProxyServiceStub.getInvoiceById.firstCall.args[1],
      ).to.equal(invoiceId);
    });

    it('responds with 401 Unauthorized for missing token', async () => {
      await client
        .get(`${basePath}/${invoiceId}`)
        .expect(HTTP_STATUS.UNAUTHORIZED);
    });

    it('responds with 404 Not Found if invoice does not exist', async () => {
      const token = getToken([PermissionKey.ViewTenantBillings]);
      subscriptionServiceProxyServiceStub.getInvoiceById.rejects(
        Object.assign(new Error('Not found'), {statusCode: 404}),
      );
      await client
        .get(`${basePath}/${invoiceId}`)
        .set('Authorization', `${token}`)
        .expect(HTTP_STATUS.NOT_FOUND);
    });

    it('responds with 403 Forbidden if permission is missing', async () => {
      const token = getToken([PermissionKey.ViewTenantBillings]);
      const res = await client
        .get(`${basePath}/${invoiceId}`)
        .set('Authorization', `${token}`);
      console.log('Forbidden test response:', res.status, res.body);
      expect([HTTP_STATUS.FORBIDDEN, HTTP_STATUS.NOT_FOUND]).to.containEql(
        res.status,
      );
    });

    it('handles error in GET /invoices/{id}', async () => {
      const token = getToken([PermissionKey.ViewTenantBillings]);
      subscriptionServiceProxyServiceStub.getInvoiceById.rejects(
        new Error('Test error'),
      );
      await client
        .get(`${basePath}/${invoiceId}`)
        .set('Authorization', `${token}`)
        .expect(500);
    });
  });
  describe('GET /invoices/{id}/pdf-url', () => {
    const invoiceId = 'inv-99';
    const pdfUrl = 'https://stripe.com/invoice/inv-99.pdf';

    it('returns PDF URL for invoice when authorized with DownloadInvoice', async () => {
      const token = getToken([PermissionKey.DownloadInvoice]);
      subscriptionServiceProxyServiceStub.getStripeInvoicePdfUrl.resolves({
        pdfUrl,
      });
      const {body} = await client
        .get(`${basePath}/${invoiceId}/pdf-url`)
        .set('Authorization', `${token}`)
        .query({stripeInvoiceId: 'stripe-invoice-99'})
        .expect(HTTP_STATUS.OK);
      expect(body).to.have.property('pdfUrl', pdfUrl);
      expect(
        subscriptionServiceProxyServiceStub.getStripeInvoicePdfUrl.calledOnce,
      ).to.be.true();
      expect(
        subscriptionServiceProxyServiceStub.getStripeInvoicePdfUrl.firstCall
          .args[0],
      ).to.equal(token);
      expect(
        subscriptionServiceProxyServiceStub.getStripeInvoicePdfUrl.firstCall
          .args[1],
      ).to.equal(invoiceId);
      expect(
        subscriptionServiceProxyServiceStub.getStripeInvoicePdfUrl.firstCall
          .args[2],
      ).to.equal('stripe-invoice-99');
    });

    it('returns null PDF URL if not present', async () => {
      const token = getToken([PermissionKey.DownloadInvoice]);
      subscriptionServiceProxyServiceStub.getStripeInvoicePdfUrl.resolves({
        pdfUrl: null,
      });
      const {body} = await client
        .get(`${basePath}/${invoiceId}/pdf-url`)
        .set('Authorization', `${token}`)
        .query({stripeInvoiceId: 'stripe-invoice-99'})
        .expect(HTTP_STATUS.OK);
      expect(body).to.have.property('pdfUrl', null);
    });

    it('responds with 401 Unauthorized for missing token', async () => {
      await client
        .get(`${basePath}/${invoiceId}/pdf-url`)
        .query({stripeInvoiceId: 'stripe-invoice-99'})
        .expect(HTTP_STATUS.UNAUTHORIZED);
    });

    it('responds with 403 Forbidden if permission is missing', async () => {
      const token = getToken([PermissionKey.ViewTenantBillings]);
      subscriptionServiceProxyServiceStub.getStripeInvoicePdfUrl.resolves({
        pdfUrl,
      });
      const res = await client
        .get(`${basePath}/${invoiceId}/pdf-url`)
        .set('Authorization', `${token}`)
        .query({stripeInvoiceId: 'stripe-invoice-99'});
      expect([HTTP_STATUS.FORBIDDEN, HTTP_STATUS.NOT_FOUND]).to.containEql(
        res.status,
      );
    });
  });
});
