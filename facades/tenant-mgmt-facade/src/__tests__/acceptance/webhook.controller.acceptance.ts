import {Client, expect} from '@loopback/testlab';
import {RestApplication, RestBindings} from '@loopback/rest';
import {setupApplication} from './test-helper';
import sinon from 'sinon';
import {Request} from '@loopback/rest';
import {NotificationProxyService} from '../../services';
import {ILogger, LOGGER} from '@sourceloop/core';
import {SubscriptionService} from '../../types';
import {NotificationTemplates} from '@local/core';
describe('WebhookController', () => {
  let app: RestApplication;
  let client: Client;
  let subscriptionServiceProxyStub: sinon.SinonStubbedInstance<SubscriptionService>;

  let loggerStub: ILogger;
  let notificationServiceProxyStub: sinon.SinonStubbedInstance<NotificationProxyService>;
  let mockRequest: Partial<Request>;
  before(async () => {
    ({app, client} = await setupApplication());
  });

  beforeEach(async () => {
    subscriptionServiceProxyStub = {
      handleWebhook: sinon.stub(),
    };

    mockRequest = {
      headers: {authorization: 'Bearer some-token'},
    };
    notificationServiceProxyStub = {
      getTemplateByName: sinon.stub(),
      createNotification: sinon.stub(),
      createBulkNotification: sinon.stub(),
    };

    loggerStub = {
      info: sinon.stub(),
      error: sinon.stub(),
      warn: sinon.stub(),
      debug: sinon.stub(),
      log: sinon.stub(), // ✅ required
    };
    app.bind(RestBindings.Http.REQUEST).to(mockRequest as Request);
    app.bind(LOGGER.LOGGER_INJECT).to(loggerStub);
    app
      .bind('services.SubscriptionProxyService')
      .to(subscriptionServiceProxyStub);
    app
      .bind('services.NotificationProxyService')
      .to(notificationServiceProxyStub);
  });

  after(async () => {
    await app.stop();
  });

  it('POST /webhook -> 204 No Content (happy path)', async () => {
    subscriptionServiceProxyStub.handleWebhook.resolves(
      JSON.stringify({
        event: 'invoice.created',
        success: true,
      }),
    );
    const res = await client
      .post('/webhook')
      .set('Content-Type', 'application/json')
      .set('stripe-signature', 't=123,v1=fake')
      .send({id: 'evt_123', type: 'invoice.created'})
      .expect(204);

    // No body on 204
    expect(res.text).to.equal('');

    // Ensure controller called the service
  });

  it('POST /webhook -> 204 No Content (happy path) with email send for first time invoice created', async () => {
    process.env.SYSTEM_USER_TENANT_ID = 'tenant-id';
    subscriptionServiceProxyStub.handleWebhook.resolves(
      JSON.stringify({
        event: 'invoice.created',
        success: true,
        sendEmail: true,
        message: 'https://payment-link.com',
      }),
    );
    notificationServiceProxyStub.getTemplateByName.resolves({
      id: 'template-id',
      eventName: 'invoice_payment_link',
      body: '<html>Test Email</html>',
      notificationType: 0,
      subject: 'Test Mail',
    } as unknown as NotificationTemplates);
    notificationServiceProxyStub.createNotification.resolves();
    const res = await client
      .post('/webhook')
      .set('Content-Type', 'application/json')
      .set('stripe-signature', 't=123,v1=fake')
      .send({id: 'evt_123', type: 'invoice.created'})
      .expect(204);

    // No body on 204
    expect(res.text).to.equal('');

    // Ensure controller called the service
  });
});
