# Path to sources
sonar.sources=.
sonar.exclusions=migrations/**,api-docs/**,**/dist/**,coverage.js,**/Dockerfile,**/dockerfile,packages/migrations/**/*,**/**/*.js,**/**/__tests__/**,packages/core/src/models/dtos/forget-password-req.dto.model.ts,packages/core/src/permissions.ts,services/orchestrator-service/src/services/*.ts,services/orchestrator-service/src/services/tenant-deployment.handler.ts,.github/**/*

# Path to tests
sonar.tests=.
sonar.test.inclusions=apps/**/src/__tests__/**,apps/**/src/tests/**

# Source encoding
sonar.sourceEncoding=UTF-8
